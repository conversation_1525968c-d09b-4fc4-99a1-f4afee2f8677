#!/usr/bin/env python3
"""
CogBridges API 应用
独立的Flask API服务器，包含所有API路由定义
"""

import time
import asyncio
from flask import Flask, jsonify, request
from flask_cors import CORS
import atexit

from utils.logger_utils import get_logger
from services.cogbridges_service import CogBridgesService

# 全局服务实例
cogbridges_service = CogBridgesService()
logger = get_logger(__name__)

def cleanup_service():
    """清理服务资源"""
    logger.info("正在关闭CogBridges服务...")
    loop = asyncio.get_event_loop()
    if loop.is_running():
        loop.create_task(cogbridges_service.close())
    else:
        asyncio.run(cogbridges_service.close())
    logger.info("CogBridges服务已关闭")

atexit.register(cleanup_service)

def get_cogbridges_service():
    """获取CogBridges服务实例"""
    return cogbridges_service


def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    CORS(app)
    
    @app.route('/test', methods=['GET'])
    def test():
        """简单测试接口"""
        return jsonify({
            "message": "Flask app is running",
            "timestamp": time.time(),
            "cogbridges_service_status": "initialized" if get_cogbridges_service() else "not_initialized"
        })
    
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """健康检查接口"""
        try:
            return jsonify({
                "status": "healthy",
                "service": "CogBridges API",
                "timestamp": time.time()
            })
        except Exception as e:
            return jsonify({
                "status": "error",
                "error": str(e)
            }), 500
    
    @app.route('/api/search', methods=['POST'])
    def search():
        """统一搜索接口 - 默认使用增强功能"""
        service = get_cogbridges_service()
        try:
            # 检查服务是否可用
            if not service:
                return jsonify({
                    "success": False,
                    "error": "CogBridges服务未初始化"
                }), 503
            
            data = request.get_json()
            query = data.get('query', '')

            if not query:
                return jsonify({
                    "success": False,
                    "error": "查询参数不能为空"
                }), 400

            # 执行搜索（同步调用异步函数）
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 执行搜索（包含LLM分析）
                result = loop.run_until_complete(
                    service.search(query)
                )

                # 转换结果为JSON格式
                response_data = {
                    "success": result.success,
                    "query": result.query,
                    "translated_query": result.translated_query,
                    "session_id": result.session_id,
                    "timestamp": result.timestamp.isoformat(),
                    "total_time": result.total_time,
                    "google_results": result.google_results,
                    "reddit_posts": result.reddit_posts,
                    "commenters_history": result.commenters_history,
                    "llm_analysis": result.llm_analysis,
                    "statistics": {
                        "translation_time": result.translation_time,
                        "google_search_time": result.google_search_time,
                        "reddit_posts_time": result.reddit_posts_time,
                        "commenters_history_time": result.commenters_history_time,
                        "llm_analysis_time": result.llm_analysis_time,
                        "google_results_count": len(result.google_results) if result.google_results else 0,
                        "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                        "commenters_count": len(result.commenters_history) if result.commenters_history else 0,
                        "similarity_analysis_count": len(result.llm_analysis.get("similarity_analysis", {})) if result.llm_analysis else 0,
                        "motivation_analysis_count": sum(len(motivations) for motivations in result.llm_analysis.get("motivation_analysis", {}).values()) if result.llm_analysis else 0
                    }
                }

                if not result.success:
                    response_data["error"] = result.error_message

                return jsonify(response_data)

            finally:
                loop.close()

        except Exception as e:
            logger.error(f"搜索请求处理失败: {e}")
            return jsonify({
                "success": False,
                "error": f"服务器内部错误: {str(e)}"
            }), 500

    @app.route('/api/history', methods=['GET'])
    def get_search_history():
        """获取搜索历史接口"""
        try:
            # 返回模拟的搜索历史数据
            # 在实际应用中，这里应该从数据库或文件中读取历史记录
            history_data = [
                {
                    "id": "1",
                    "query": "如何度过创业低谷期",
                    "timestamp": time.time() - 3600,  # 1小时前
                    "resultsCount": 5,
                    "searchTime": 43.01,
                    "success": True
                },
                {
                    "id": "2",
                    "query": "30岁转行程序员来得及吗",
                    "timestamp": time.time() - 86400,  # 1天前
                    "resultsCount": 3,
                    "searchTime": 35.2,
                    "success": True
                }
            ]

            return jsonify(history_data)

        except Exception as e:
            return jsonify({
                "error": f"获取搜索历史失败: {str(e)}"
            }), 500

    @app.route('/api/bookmarks', methods=['GET'])
    def get_bookmarks():
        """获取收藏列表接口"""
        try:
            # 返回模拟的收藏数据
            # 在实际应用中，这里应该从数据库中读取收藏记录
            bookmarks_data = [
                {
                    "id": "bookmark_1",
                    "query": "如何度过创业低谷期",
                    "title": "创业低谷期的心理调适",
                    "content": "创业路上遇到低谷是正常的...",
                    "source": "r/entrepreneur",
                    "timestamp": time.time() - 7200,  # 2小时前
                    "tags": ["创业", "心理健康", "经验分享"]
                }
            ]

            return jsonify(bookmarks_data)

        except Exception as e:
            return jsonify({
                "error": f"获取收藏列表失败: {str(e)}"
            }), 500

    @app.route('/api/bookmarks', methods=['POST'])
    def add_bookmark():
        """添加收藏接口"""
        try:
            data = request.get_json()

            # 在实际应用中，这里应该将数据保存到数据库
            bookmark_id = f"bookmark_{int(time.time())}"

            return jsonify({
                "success": True,
                "bookmark_id": bookmark_id,
                "message": "收藏添加成功"
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"添加收藏失败: {str(e)}"
            }), 500

    @app.route('/api/bookmarks/<bookmark_id>', methods=['DELETE'])
    def remove_bookmark(bookmark_id):
        """删除收藏接口"""
        try:
            # 在实际应用中，这里应该从数据库中删除对应的收藏记录

            return jsonify({
                "success": True,
                "message": f"收藏 {bookmark_id} 删除成功"
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"删除收藏失败: {str(e)}"
            }), 500

    @app.route('/api/status', methods=['GET'])
    def get_status():
        """获取服务状态接口"""
        service = get_cogbridges_service()
        try:
            if not service:
                return jsonify({
                    "service": "CogBridges API",
                    "timestamp": time.time(),
                    "status": "service_unavailable",
                    "error": "CogBridges服务未初始化"
                }), 503
            
            stats = service.get_statistics()

            # 检查各个服务的状态
            status_info = {
                "service": "CogBridges API",
                "timestamp": time.time(),
                "version": "2.0.0",
                "features": {
                    "basic_search": True,
                    "enhanced_comments": True,
                    "llm_analysis": True
                },
                "services": {
                    "google_search": bool(stats.get("google_stats", {}).get("configured", False)),
                    "reddit_api": bool(stats.get("reddit_stats", {}).get("configured", False)),
                    "llm_service": bool(stats.get("llm_stats", {}).get("configured", False))
                },
                "statistics": stats
            }

            return jsonify(status_info)

        except Exception as e:
            logger.error(f"状态查询失败: {e}")
            return jsonify({
                "service": "CogBridges API",
                "timestamp": time.time(),
                "error": f"状态查询失败: {str(e)}"
            }), 500
    
    return app


def run_app(host="localhost", port=5000, debug=False):
    """运行Flask应用"""
    app = create_app()
    app.run(host=host, port=port, debug=debug, threaded=True)


if __name__ == "__main__":
    from config import config
    run_app(host=config.HOST, port=config.PORT, debug=False) 