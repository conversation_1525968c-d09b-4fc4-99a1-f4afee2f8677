import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, Clock, Bookmark, Search, Trash2, Download, AlertCircle } from 'lucide-react'
import { api } from '../services/api'

const HistoryPage = () => {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('history') // history, bookmarks
  const [searchHistory, setSearchHistory] = useState([])
  const [bookmarks, setBookmarks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // 加载数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      setError(null)
      
      try {
        // 并行加载历史记录和收藏
        const [historyData, bookmarksData] = await Promise.all([
          api.getSearchHistory().catch(() => []), // 如果失败返回空数组
          api.getBookmarks().catch(() => [])
        ])
        
        setSearchHistory(historyData)
        setBookmarks(bookmarksData)
      } catch (err) {
        console.error('加载数据失败:', err)
        setError('加载数据失败，将显示本地模拟数据')
        
        // 使用模拟数据作为后备
        const mockHistory = [
          {
            id: '1',
            query: '我该不该裸辞？',
            timestamp: Date.now() - 3600000, // 1小时前
            resultsCount: 3,
            searchTime: 3.2
          },
          {
            id: '2', 
            query: '30岁转行程序员来得及吗？',
            timestamp: Date.now() - 86400000, // 1天前
            resultsCount: 5,
            searchTime: 4.1
          }
        ]

        const mockBookmarks = [
          {
            id: 'b1',
            author: 'CareerAdvice_Pro',
            query: '我该不该裸辞？',
            summary: '建议先做好充分准备再裸辞，包括资金储备和工作机会',
            subreddit: 'careerguidance',
            score: 128,
            bookmarkedAt: Date.now() - 1800000, // 30分钟前
            tags: ['理性思考', '亲身经历']
          }
        ]
        
        setSearchHistory(mockHistory)
        setBookmarks(mockBookmarks)
      } finally {
        setLoading(false)
      }
    }
    
    loadData()
  }, [])

  const formatTimeAgo = (timestamp) => {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (days > 0) return `${days}天前`
    if (hours > 0) return `${hours}小时前`
    if (minutes > 0) return `${minutes}分钟前`
    return '刚刚'
  }

  const handleRerunSearch = async (query) => {
    try {
      setLoading(true)
      const searchResult = await api.search(query)
      
      navigate('/loading', { 
        state: { 
          query,
          sessionId: searchResult.session_id || Date.now().toString(),
          searchResult,
          timestamp: Date.now()
        } 
      })
    } catch (error) {
      alert(`搜索失败: ${error.message}`)
      setLoading(false)
    }
  }

  const handleRemoveHistory = async (id) => {
    try {
      // 尝试调用API删除（如果后端支持）
      // await api.removeHistory(id)
      
      // 本地更新
      setSearchHistory(prev => prev.filter(item => item.id !== id))
    } catch (error) {
      console.error('删除历史记录失败:', error)
    }
  }

  const handleRemoveBookmark = async (id) => {
    try {
      await api.removeBookmark(id)
      setBookmarks(prev => prev.filter(item => item.id !== id))
    } catch (error) {
      console.error('删除收藏失败:', error)
      alert('删除收藏失败，请重试')
    }
  }

  const handleExportData = () => {
    const data = {
      searchHistory,
      bookmarks,
      exportedAt: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(data, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `cogbridges-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">正在加载数据...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/')}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              
              <div>
                <h1 className="text-xl font-semibold text-gray-800">我的记录</h1>
                <p className="text-sm text-gray-500">搜索历史和收藏管理</p>
              </div>
            </div>

            <button
              onClick={handleExportData}
              className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span className="text-sm">导出数据</span>
            </button>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-6 py-6">
        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center space-x-3">
            <AlertCircle className="w-5 h-5 text-yellow-500" />
            <div>
              <p className="text-yellow-800 font-medium">数据加载提示</p>
              <p className="text-yellow-700 text-sm">{error}</p>
            </div>
          </div>
        )}

        {/* 标签切换 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="flex border-b border-gray-200">
            <button
              onClick={() => setActiveTab('history')}
              className={`flex-1 px-6 py-4 text-center font-medium transition-colors ${
                activeTab === 'history'
                  ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>搜索历史</span>
                <span className="bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">
                  {searchHistory.length}
                </span>
              </div>
            </button>
            
            <button
              onClick={() => setActiveTab('bookmarks')}
              className={`flex-1 px-6 py-4 text-center font-medium transition-colors ${
                activeTab === 'bookmarks'
                  ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <Bookmark className="w-4 h-4" />
                <span>我的收藏</span>
                <span className="bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">
                  {bookmarks.length}
                </span>
              </div>
            </button>
          </div>
        </div>

        {/* 搜索历史 */}
        {activeTab === 'history' && (
          <div className="space-y-4">
            {searchHistory.length === 0 ? (
              <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
                <Clock className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">还没有搜索历史</h3>
                <p className="text-gray-500 mb-4">开始你的第一次智能搜索吧</p>
                <button
                  onClick={() => navigate('/')}
                  className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
                >
                  开始搜索
                </button>
              </div>
            ) : (
              searchHistory.map((item) => (
                <div key={item.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-800 mb-2">
                        "{item.query}"
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        <span>{formatTimeAgo(item.timestamp)}</span>
                        {item.resultsCount && (
                          <>
                            <span>•</span>
                            <span>找到 {item.resultsCount} 个结果</span>
                          </>
                        )}
                        {item.searchTime && (
                          <>
                            <span>•</span>
                            <span>耗时 {item.searchTime} 秒</span>
                          </>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleRerunSearch(item.query)}
                        className="flex items-center space-x-2 px-4 py-2 text-primary-600 hover:text-primary-700 hover:bg-primary-50 rounded-lg transition-colors"
                      >
                        <Search className="w-4 h-4" />
                        <span>重新搜索</span>
                      </button>
                      
                      <button
                        onClick={() => handleRemoveHistory(item.id)}
                        className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="删除记录"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {/* 收藏列表 */}
        {activeTab === 'bookmarks' && (
          <div className="space-y-4">
            {bookmarks.length === 0 ? (
              <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
                <Bookmark className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">还没有收藏内容</h3>
                <p className="text-gray-500 mb-4">收藏有价值的回答，方便日后查看</p>
                <button
                  onClick={() => navigate('/')}
                  className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
                >
                  开始搜索
                </button>
              </div>
            ) : (
              bookmarks.map((item) => (
                <div key={item.id} className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-sm font-medium text-primary-600">u/{item.author}</span>
                        {item.subreddit && (
                          <>
                            <span className="text-gray-300">•</span>
                            <span className="text-sm text-gray-500">r/{item.subreddit}</span>
                          </>
                        )}
                        {item.score && (
                          <>
                            <span className="text-gray-300">•</span>
                            <span className="text-sm text-gray-500">{item.score} 赞</span>
                          </>
                        )}
                      </div>
                      
                      <h3 className="text-lg font-medium text-gray-800 mb-2">
                        关于 "{item.query}"
                      </h3>
                      
                      <p className="text-gray-700 mb-3 leading-relaxed">
                        {item.summary}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex flex-wrap gap-2">
                          {item.tags?.map((tag, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-primary-100 text-primary-700 rounded-full text-xs"
                            >
                              #{tag}
                            </span>
                          ))}
                        </div>
                        
                        <span className="text-xs text-gray-500">
                          收藏于 {formatTimeAgo(item.bookmarkedAt)}
                        </span>
                      </div>
                    </div>
                    
                    <button
                      onClick={() => handleRemoveBookmark(item.id)}
                      className="ml-4 p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="取消收藏"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default HistoryPage 