"""
测试用户评论动机分析功能
"""

import asyncio
import json
from datetime import datetime
from services.llm_service import llm_service
from utils.logger_utils import get_logger

logger = get_logger(__name__)

async def test_comment_motivation_analysis():
    """测试用户评论动机分析功能"""
    logger.info("开始测试用户评论动机分析功能")
    
    # 模拟测试数据
    test_cases = [
        {
            "user_id": "haslo",
            "target_subreddit": "ClaudeAI",
            "target_post": {
                "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspective on AI assistants",
                "author": "irukadesune",
                "score": 225,
                "num_comments": 140,
                "selftext": "As a subscriber to both <PERSON> and ChatGPT, I've been comparing their performance to decide which one to keep. Here's my experience:\n\nCoding: As a programmer, I've found <PERSON> to be exceptionally impressive. In my experience, it consistently produces nearly bug-free code on the first try, outperforming GPT-4 in this area.\n\nText Summarization: I recently tested both models on summarizing a PDF of my monthly spending transactions. <PERSON>'s summary was not only more accurate but also delivered in a smart, human-like style. In contrast, GPT-4's summary contained errors and felt robotic and unengaging.\n\nOverall Experience: While I was initially excited about GPT-4's release (ChatGPT was my first-ever online subscription), using Claude has changed my perspective. Returning to GPT-4 after using Claude feels like a step backward, reminiscent of using GPT-3.5.\n\nIn conclusion, Claude 3.5 Sonnet has impressed me with its coding prowess, accurate summarization, and natural communication style. It's challenging my assumption that GPT-4 is the current \"state of the art\" in AI language models.\n\nI'm curious to hear about others' experiences. Have you used both models? How do they compare in your use cases?"
            },
            "user_comment": {
                "body": "Yes. Claude is better in every aspect except for limits.\n\nSo I currently have both. Using Claude as long as I can, selectively with longer prompts. And ChatGPT for the rest.\n\nI did have two ChatGPT subscriptions. Killed one of them for Claude.",
                "score": 57,
                "created_utc": 1719580563.0
            },
            "similar_subreddits_data": [
                {
                    "subreddit": "programming",
                    "posts": ["post1", "post2"],
                    "comments": ["comment1", "comment2"],
                    "user_engagement": "high"
                },
                {
                    "subreddit": "python",
                    "posts": ["post3"],
                    "comments": ["comment3"],
                    "user_engagement": "medium"
                }
            ],
            "user_overview": {
                "subreddits": ["programming", "python", "javascript", "webdev", "datascience"],
                "user_type": "developer",
                "activity_level": "high"
            },
            "description": "程序员用户对Claude vs GPT-4比较帖子的评论分析"
        },
        {
            "user_id": "HighPurrFormer",
            "target_subreddit": "ClaudeAI",
            "target_post": {
                "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspective on AI assistants",
                "author": "irukadesune",
                "score": 225,
                "num_comments": 140,
                "selftext": "As a subscriber to both Claude and ChatGPT, I've been comparing their performance to decide which one to keep. Here's my experience:\n\nCoding: As a programmer, I've found Claude to be exceptionally impressive. In my experience, it consistently produces nearly bug-free code on the first try, outperforming GPT-4 in this area.\n\nText Summarization: I recently tested both models on summarizing a PDF of my monthly spending transactions. Claude's summary was not only more accurate but also delivered in a smart, human-like style. In contrast, GPT-4's summary contained errors and felt robotic and unengaging.\n\nOverall Experience: While I was initially excited about GPT-4's release (ChatGPT was my first-ever online subscription), using Claude has changed my perspective. Returning to GPT-4 after using Claude feels like a step backward, reminiscent of using GPT-3.5.\n\nIn conclusion, Claude 3.5 Sonnet has impressed me with its coding prowess, accurate summarization, and natural communication style. It's challenging my assumption that GPT-4 is the current \"state of the art\" in AI language models.\n\nI'm curious to hear about others' experiences. Have you used both models? How do they compare in your use cases?"
            },
            "user_comment": {
                "body": "Well, I know nothing about code, not a single thing. I managed to create two games using python and running them in VS Code using Claude to not only generate the code, but I also helped me troubleshoot the issues I was having. It walked me through each code update when asking it to \"make it better.\" I'm currently trying to decide between Claude of ChatGPT for my next step.",
                "score": 6,
                "created_utc": 1719626674.0
            },
            "similar_subreddits_data": [
                {
                    "subreddit": "gaming",
                    "posts": ["post1"],
                    "comments": ["comment1"],
                    "user_engagement": "medium"
                }
            ],
            "user_overview": {
                "subreddits": ["gaming", "movies", "books", "music"],
                "user_type": "enthusiast",
                "activity_level": "medium"
            },
            "description": "非技术用户对Claude编程能力的评论分析"
        },
        {
            "user_id": "Kathane37",
            "target_subreddit": "ClaudeAI",
            "target_post": {
                "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspective on AI assistants",
                "author": "irukadesune",
                "score": 225,
                "num_comments": 140,
                "selftext": "As a subscriber to both Claude and ChatGPT, I've been comparing their performance to decide which one to keep. Here's my experience:\n\nCoding: As a programmer, I've found Claude to be exceptionally impressive. In my experience, it consistently produces nearly bug-free code on the first try, outperforming GPT-4 in this area.\n\nText Summarization: I recently tested both models on summarizing a PDF of my monthly spending transactions. Claude's summary was not only more accurate but also delivered in a smart, human-like style. In contrast, GPT-4's summary contained errors and felt robotic and unengaging.\n\nOverall Experience: While I was initially excited about GPT-4's release (ChatGPT was my first-ever online subscription), using Claude has changed my perspective. Returning to GPT-4 after using Claude feels like a step backward, reminiscent of using GPT-3.5.\n\nIn conclusion, Claude 3.5 Sonnet has impressed me with its coding prowess, accurate summarization, and natural communication style. It's challenging my assumption that GPT-4 is the current \"state of the art\" in AI language models.\n\nI'm curious to hear about others' experiences. Have you used both models? How do they compare in your use cases?"
            },
            "user_comment": {
                "body": "Test Claude on graph information retrieval\nThis is an other hidden skill where it crush 4o",
                "score": 16,
                "created_utc": 1719580188.0
            },
            "similar_subreddits_data": [
                {
                    "subreddit": "datascience",
                    "posts": ["post1", "post2", "post3"],
                    "comments": ["comment1", "comment2"],
                    "user_engagement": "high"
                },
                {
                    "subreddit": "machinelearning",
                    "posts": ["post4"],
                    "comments": ["comment3"],
                    "user_engagement": "high"
                }
            ],
            "user_overview": {
                "subreddits": ["datascience", "machinelearning", "python", "statistics"],
                "user_type": "researcher",
                "activity_level": "high"
            },
            "description": "数据科学家用户对Claude信息检索能力的评论分析"
        },
        {
            "user_id": "OfficeSalamander",
            "target_subreddit": "ClaudeAI",
            "target_post": {
                "title": "Claude 3.5 Sonnet vs GPT-4: A programmer's perspective on AI assistants",
                "author": "irukadesune",
                "score": 225,
                "num_comments": 140,
                "selftext": "As a subscriber to both Claude and ChatGPT, I've been comparing their performance to decide which one to keep. Here's my experience:\n\nCoding: As a programmer, I've found Claude to be exceptionally impressive. In my experience, it consistently produces nearly bug-free code on the first try, outperforming GPT-4 in this area.\n\nText Summarization: I recently tested both models on summarizing a PDF of my monthly spending transactions. Claude's summary was not only more accurate but also delivered in a smart, human-like style. In contrast, GPT-4's summary contained errors and felt robotic and unengaging.\n\nOverall Experience: While I was initially excited about GPT-4's release (ChatGPT was my first-ever online subscription), using Claude has changed my perspective. Returning to GPT-4 after using Claude feels like a step backward, reminiscent of using GPT-3.5.\n\nIn conclusion, Claude 3.5 Sonnet has impressed me with its coding prowess, accurate summarization, and natural communication style. It's challenging my assumption that GPT-4 is the current \"state of the art\" in AI language models.\n\nI'm curious to hear about others' experiences. Have you used both models? How do they compare in your use cases?"
            },
            "user_comment": {
                "body": "Yeah, I find Claude FAR more correct. I basically don't even use ChatGPT anymore",
                "score": 6,
                "created_utc": 1719586979.0
            },
            "similar_subreddits_data": [
                {
                    "subreddit": "technology",
                    "posts": ["post1", "post2"],
                    "comments": ["comment1"],
                    "user_engagement": "medium"
                }
            ],
            "user_overview": {
                "subreddits": ["technology", "programming", "gaming", "movies"],
                "user_type": "tech_enthusiast",
                "activity_level": "medium"
            },
            "description": "技术爱好者用户对Claude准确性的评论分析"
        }
    ]
    
    async def analyze_single_user(case):
        """分析单个用户的评论动机"""
        try:
            logger.info(f"开始分析用户: {case['user_id']} - {case['description']}")
            
            result = await llm_service.analyze_user_comment_motivation(
                user_id=case["user_id"],
                target_subreddit=case["target_subreddit"],
                target_post=case["target_post"],
                user_comment=case["user_comment"],
                similar_subreddits_data=case["similar_subreddits_data"],
                user_overview=case["user_overview"]
            )
            
            logger.info(f"用户 {case['user_id']} 分析完成")
            return {
                "test_case": case["description"],
                "result": result
            }
            
        except Exception as e:
            logger.error(f"用户 {case['user_id']} 分析失败: {e}")
            return {
                "test_case": case["description"],
                "error": str(e)
            }
    
    # 并行执行所有用户分析
    logger.info(f"开始并行分析 {len(test_cases)} 个用户...")
    tasks = [analyze_single_user(case) for case in test_cases]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理异常结果
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"用户 {test_cases[i]['user_id']} 分析异常: {result}")
            processed_results.append({
                "test_case": test_cases[i]["description"],
                "error": str(result)
            })
        else:
            processed_results.append(result)
    
    return processed_results

async def main():
    """主测试函数"""
    logger.info("开始用户评论动机分析功能测试")
    
    # 检查LLM服务配置
    if not llm_service.configured:
        logger.error("LLM服务未配置，无法进行测试")
        logger.info("请在.env文件中配置REPLICATE_API_TOKEN以启用LLM功能")
        return
    
    # 测试连接
    logger.info("测试LLM服务连接...")
    if not await llm_service.test_connection():
        logger.error("LLM服务连接失败，跳过测试")
        return
    
    # 执行测试
    start_time = datetime.now()
    test_results = {
        "timestamp": start_time.isoformat(),
        "test_cases": await test_comment_motivation_analysis()
    }
    end_time = datetime.now()
    
    # 计算总耗时
    total_time = (end_time - start_time).total_seconds()
    logger.info(f"所有测试完成，总耗时: {total_time:.2f}秒")
    
    # 保存测试结果
    output_file = f"test_results/comment_motivation_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        import os
        os.makedirs("test_results", exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试结果已保存到: {output_file}")
        
    except Exception as e:
        logger.error(f"保存测试结果失败: {e}")
    
    # 输出统计信息
    stats = llm_service.get_stats()
    logger.info(f"LLM服务统计: {stats}")
    
    logger.info("用户评论动机分析功能测试完成")

if __name__ == "__main__":
    asyncio.run(main()) 