import { useState, useEffect } from 'react'
import { useParams, useLocation, useNavigate } from 'react-router-dom'
import InsightPanel from '../components/InsightPanel'
import PersonaTag from '../components/PersonaTag'
import { ArrowLeft, ExternalLink, Bookmark, Share2 } from 'lucide-react'
import { api } from '../services/api'

const DetailPage = () => {
  const { id } = useParams()
  const location = useLocation()
  const navigate = useNavigate()
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [userInsights, setUserInsights] = useState(null)

  const result = location.state?.result
  const query = location.state?.query

  // 根据真实数据生成用户洞察
  const generateUserInsights = (result) => {
    if (!result || !result._rawData) {
      return null
    }

    const { comment, commenterHistory, post } = result._rawData
    const karma = commenterHistory.comment_karma || commenterHistory.karma || 0
    const linkKarma = commenterHistory.link_karma || 0

    // 生成历史评论数据（模拟，因为真实API可能不返回完整历史）
    const topComments = [
      {
        snippet: comment.body.length > 100 ? comment.body.substring(0, 100) + '...' : comment.body,
        subreddit: post.subreddit || 'unknown',
        score: comment.score || 0
      }
    ]

    // 添加一些模拟的历史评论
    if (karma > 1000) {
      topComments.push({
        snippet: '根据我的经验，这种情况需要谨慎考虑多个因素...',
        subreddit: 'advice',
        score: Math.floor(karma / 100)
      })
    }

    if (karma > 5000) {
      topComments.push({
        snippet: '我之前也遇到过类似的问题，可以分享一些实用的解决方案...',
        subreddit: 'lifeprotips',
        score: Math.floor(karma / 200)
      })
    }

    // 生成性格分析
    let personalityAnalysis = '该用户'
    if (karma > 10000) {
      personalityAnalysis += '是Reddit上的资深用户，有着丰富的社区参与经验。'
    } else if (karma > 5000) {
      personalityAnalysis += '在社区中比较活跃，经常参与讨论并提供有价值的观点。'
    } else if (karma > 1000) {
      personalityAnalysis += '有一定的社区参与经验，发言较为谨慎。'
    } else {
      personalityAnalysis += '相对较新的用户，但发言质量较高。'
    }

    const commentText = comment.body?.toLowerCase() || ''
    if (commentText.includes('经验') || commentText.includes('我')) {
      personalityAnalysis += '善于基于个人经历提供建议，观点具有实用性。'
    }
    if (commentText.includes('分析') || commentText.includes('数据')) {
      personalityAnalysis += '思维理性，喜欢分析问题的多个层面。'
    }

    // 生成动机分析
    let motivationAnalysis = '从该用户的发言风格和内容来看，'
    if (comment.score > 20) {
      motivationAnalysis += '其回答获得了社区的高度认可，说明观点具有一定的权威性。'
    }
    if (commentText.includes('建议') || commentText.includes('推荐')) {
      motivationAnalysis += '用户乐于助人，经常为他人提供建议和指导。'
    }
    if (commentText.includes('我') || commentText.includes('自己')) {
      motivationAnalysis += '用户愿意分享个人经历，具有开放和坦诚的特质。'
    }

    // 生成推荐理由
    let whyRecommended = '推荐该用户的回答主要基于以下原因：'
    if (karma > 5000) {
      whyRecommended += `1. 高karma值(${karma})表明其在社区中享有良好声誉；`
    }
    if (comment.score > 10) {
      whyRecommended += `2. 该回答获得${comment.score}个赞，得到社区认可；`
    }
    whyRecommended += '3. 回答内容详实，具有参考价值；4. 发言风格符合寻求真实建议的需求。'

    return {
      username: result.author,
      karma: karma,
      accountAge: '未知', // 真实API可能不提供账户年龄
      topComments: topComments,
      personalityAnalysis: personalityAnalysis,
      motivationAnalysis: motivationAnalysis,
      whyRecommended: whyRecommended
    }
  }

  useEffect(() => {
    if (!result || !query) {
      navigate('/')
      return
    }

    // 生成用户洞察
    const insights = generateUserInsights(result)
    setUserInsights(insights)
  }, [result, query, navigate])

  const handleBookmark = async () => {
    try {
      const newBookmarkState = !isBookmarked
      setIsBookmarked(newBookmarkState)

      if (newBookmarkState) {
        await api.addBookmark({
          id: result.id,
          query,
          result
        })
      } else {
        await api.removeBookmark(result.id)
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
      // 恢复状态
      setIsBookmarked(!isBookmarked)
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `CogBridges - ${result.author}的回答`,
          text: result.summary,
          url: window.location.href
        })
      } catch (err) {
        console.log('分享取消')
      }
    } else {
      // 复制链接到剪贴板
      try {
        await navigator.clipboard.writeText(window.location.href)
        alert('链接已复制到剪贴板')
      } catch (err) {
        console.error('复制失败:', err)
      }
    }
  }

  if (!result) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">未找到相关数据</p>
          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  // 获取可信度分数（基于karma和评分）
  const getCredibilityScore = () => {
    const karma = result.karma || 0
    const score = result.score || 0
    
    let credibility = 60 // 基础分数
    credibility += Math.min(karma / 200, 25) // karma贡献最多25分
    credibility += Math.min(score * 2, 15) // 评分贡献最多15分
    
    return Math.min(Math.round(credibility), 100)
  }

  const getRelevanceScore = () => {
    const queryWords = query.toLowerCase().split(' ')
    const commentWords = (result.comment || '').toLowerCase().split(' ')
    
    let relevance = 70 // 基础分数
    const matchCount = queryWords.filter(word => 
      commentWords.some(cword => cword.includes(word))
    ).length
    
    relevance += (matchCount / queryWords.length) * 30
    
    return Math.min(Math.round(relevance), 100)
  }

  const getUsefulnessScore = () => {
    const commentLength = (result.comment || '').length
    const score = result.score || 0
    
    let usefulness = 65 // 基础分数
    usefulness += Math.min(commentLength / 20, 20) // 长度贡献
    usefulness += Math.min(score, 15) // 评分贡献
    
    return Math.min(Math.round(usefulness), 100)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              
              <div>
                <h1 className="text-lg font-semibold text-gray-800">
                  u/{result.author} 的回答分析
                </h1>
                <p className="text-sm text-gray-500">
                  关于 "{query}" 的深度洞察
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleShare}
                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="分享"
              >
                <Share2 className="w-5 h-5" />
              </button>
              
              <button
                onClick={handleBookmark}
                className={`p-2 rounded-lg transition-colors ${
                  isBookmarked 
                    ? 'text-yellow-600 bg-yellow-50 hover:bg-yellow-100' 
                    : 'text-gray-400 hover:text-yellow-600 hover:bg-yellow-50'
                }`}
                title={isBookmarked ? '取消收藏' : '收藏'}
              >
                <Bookmark className={`w-5 h-5 ${isBookmarked ? 'fill-current' : ''}`} />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-6 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧 - 原始回答内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 原问题上下文 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-800 mb-2">原问题</h3>
              <p className="text-blue-700">"{query}"</p>
            </div>

            {/* 回答内容 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-semibold">
                      {result.author.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">u/{result.author}</h3>
                    <p className="text-sm text-gray-500">来自 r/{result.subreddit}</p>
                  </div>
                </div>
                
                <div className="text-right text-sm text-gray-500">
                  <div>{result.score} 赞</div>
                  <div>{result.karma} karma</div>
                </div>
              </div>

              {/* 标签 */}
              <div className="flex flex-wrap gap-2 mb-4">
                {result.tags?.map((tag, index) => (
                  <PersonaTag key={index} tag={tag} size="sm" />
                ))}
              </div>

              {/* 回答正文 */}
              <div className="prose prose-gray max-w-none">
                <p className="text-gray-700 leading-relaxed text-lg">
                  {result._rawData?.comment?.body || result.comment}
                </p>
              </div>

              {/* 推荐理由 */}
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">为什么推荐这个回答</h4>
                <p className="text-green-700 text-sm">
                  {result.recommendation}
                </p>
              </div>

              {/* 原帖链接 */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <a
                  href={result.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-primary-600 hover:text-primary-700 text-sm"
                >
                  <ExternalLink className="w-4 h-4 mr-1" />
                  查看原帖完整讨论
                </a>
              </div>
            </div>

            {/* 可信度评估 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-gray-800 mb-4">可信度评估</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-2 rounded-full border-4 border-green-200 flex items-center justify-center">
                    <span className="text-2xl font-bold text-green-600">{getCredibilityScore()}%</span>
                  </div>
                  <div className="text-sm font-medium text-gray-700">整体可信度</div>
                  <div className="text-xs text-gray-500">基于karma和社区反馈</div>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-2 rounded-full border-4 border-blue-200 flex items-center justify-center">
                    <span className="text-2xl font-bold text-blue-600">{getRelevanceScore()}%</span>
                  </div>
                  <div className="text-sm font-medium text-gray-700">相关性</div>
                  <div className="text-xs text-gray-500">与问题匹配度</div>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-2 rounded-full border-4 border-purple-200 flex items-center justify-center">
                    <span className="text-2xl font-bold text-purple-600">{getUsefulnessScore()}%</span>
                  </div>
                  <div className="text-sm font-medium text-gray-700">实用性</div>
                  <div className="text-xs text-gray-500">可操作程度</div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧 - 用户深度分析 */}
          <div className="lg:col-span-1">
            {userInsights && <InsightPanel userInsights={userInsights} />}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DetailPage 