"""
CogBridges Search - Google搜索服务
使用Google Custom Search API实现搜索，支持Reddit内容搜索
"""

import time
from typing import List, Dict, Any
import requests
import aiohttp
from tenacity import retry, stop_after_attempt, wait_exponential

from config import config
from models.search_models import SearchQuery, SearchResult, GoogleSearchResult
from utils.logger_utils import get_logger, log_api_call, log_search_operation



class GoogleSearchService:
    """Google搜索服务类 - 使用Google Custom Search API"""

    def __init__(self):
        """初始化Google搜索服务"""
        self.logger = get_logger(__name__)

        # 验证API配置
        if not config.google_search_configured:
            self.logger.error("Google Custom Search API未配置")
            raise ValueError("Google Custom Search API配置不完整")

        # Google Custom Search API配置
        self.api_key = config.GOOGLE_API_KEY
        self.engine_id = config.GOOGLE_SEARCH_ENGINE_ID
        self.api_url = "https://www.googleapis.com/customsearch/v1"

        # 请求统计
        self.request_count = 0
        self.total_search_time = 0.0

        self.logger.info("Google搜索服务初始化成功（Custom Search API模式）")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10)
    )
    @log_api_call("Google Custom Search", "search", "GET")
    async def search(self, query: str, max_results: int = 10, **kwargs) -> SearchResult:
        """
        使用Google Custom Search API执行异步搜索

        Args:
            query: 搜索查询字符串
            max_results: 返回的最大结果数量
            **kwargs: 其他API参数

        Returns:
            搜索结果对象
        """
        start_time = time.time()
        search_query = SearchQuery(
            query=f"site:reddit.com {query}",
            max_results=max_results
        )

        params = {
            "key": self.api_key,
            "cx": self.engine_id,
            "q": search_query.query,
            "num": search_query.max_results,
            "safe": "off"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.api_url, params=params) as response:
                    response.raise_for_status()
                    data = await response.json()

            search_time = time.time() - start_time
            self.total_search_time += search_time
            self.request_count += 1

            if "items" not in data:
                self.logger.warning(f"Google搜索'{search_query.query}'未返回任何结果。")
                return SearchResult(query=search_query, success=False, error_message="No results found")

            results = []
            for i, item in enumerate(data["items"]):
                result = GoogleSearchResult(
                    rank=i + 1,
                    title=item.get("title", ""),
                    url=item.get("link", ""),
                    snippet=item.get("snippet", "")
                )
                results.append(result)

            total_results = int(data.get("searchInformation", {}).get("totalResults", 0))

            log_search_operation(
                query=search_query.query,
                results_count=len(results),
                duration=search_time
            )
            self.logger.info(f"Google搜索完成: 找到 {len(results)} 个结果（总计 {total_results}），耗时 {search_time:.2f}秒")

            return SearchResult(
                query=search_query,
                results=results,
                success=True,
                total_results=total_results,
                search_time=search_time
            )

        except aiohttp.ClientError as e:
            self.logger.error(f"Google Custom Search API请求失败: {e}")
            return SearchResult(query=search_query, success=False, error_message=str(e))
        except Exception as e:
            self.logger.error(f"处理Google搜索结果时发生未知错误: {e}")
            return SearchResult(query=search_query, success=False, error_message=str(e))

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取服务统计信息

        Returns:
            统计信息字典
        """
        return {
            'request_count': self.request_count,
            'total_search_time': self.total_search_time,
            'average_search_time': (
                self.total_search_time / self.request_count
                if self.request_count > 0 else 0
            ),
            'search_method': 'Google Custom Search API',
            'api_configured': config.google_search_configured
        } 