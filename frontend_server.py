#!/usr/bin/env python3
"""
CogBridges 前端服务器启动脚本
独立的React前端开发服务器启动器
"""

import sys
import os
import subprocess
import threading
import time
import webbrowser
import socket
import psutil
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger_utils import get_logger


class FrontendServer:
    """前端服务器启动器"""
    
    def __init__(self):
        """初始化前端服务器"""
        self.logger = get_logger(__name__)
        self.frontend_process = None
        self.running = False
        
        # 前端目录
        self.frontend_dir = project_root / "frontend"
        self.frontend_port = config.PORT + 1
    
    def check_port(self, port, host="localhost"):
        """检查端口占用"""
        print(f"🔍 检查前端端口 {host}:{port} 占用情况...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"⚠️ 前端端口 {port} 已被占用")
                return False
            else:
                print(f"✅ 前端端口 {port} 可用")
                return True
                
        except Exception as e:
            self.logger.error(f"检查前端端口 {port} 时出错: {e}")
            print(f"⚠️ 检查前端端口 {port} 时出错: {e}")
            return False
    
    def cleanup_port(self, port, host="localhost"):
        """清理端口占用"""
        print(f"🧹 正在清理前端端口 {host}:{port}...")
        
        try:
            # 查找占用端口的进程
            processes_using_port = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    connections = proc.connections()
                    for conn in connections:
                        if conn.laddr.port == port:
                            processes_using_port.append({
                                'pid': proc.pid,
                                'name': proc.name(),
                                'cmdline': ' '.join(proc.cmdline()) if proc.cmdline() else ''
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            if processes_using_port:
                print(f"📋 发现 {len(processes_using_port)} 个进程占用前端端口 {port}:")
                for proc_info in processes_using_port:
                    print(f"   - PID: {proc_info['pid']}, 名称: {proc_info['name']}")
                
                # 询问是否清理端口
                try:
                    response = input(f"\n❓ 是否终止占用前端端口 {port} 的进程？(y/N): ").strip().lower()
                    if response in ['y', 'yes']:
                        killed_count = 0
                        for proc_info in processes_using_port:
                            try:
                                proc = psutil.Process(proc_info['pid'])
                                proc.terminate()
                                proc.wait(timeout=5)
                                print(f"✅ 已终止进程 PID {proc_info['pid']} ({proc_info['name']})")
                                killed_count += 1
                            except (psutil.NoSuchProcess, psutil.TimeoutExpired, psutil.AccessDenied) as e:
                                print(f"⚠️ 无法终止进程 PID {proc_info['pid']}: {e}")
                        
                        if killed_count > 0:
                            print(f"✅ 已清理 {killed_count} 个占用端口的进程")
                            time.sleep(2)
                            return True
                        else:
                            print("❌ 未能清理任何进程")
                            return False
                    else:
                        print("⏭️ 跳过端口清理")
                        return False
                except KeyboardInterrupt:
                    print("\n⏭️ 用户取消，跳过端口清理")
                    return False
            else:
                print(f"⚠️ 前端端口 {port} 被占用但无法识别占用进程")
                return False
                
        except Exception as e:
            self.logger.error(f"清理前端端口 {port} 时出错: {e}")
            print(f"⚠️ 清理前端端口 {port} 时出错: {e}")
            return False
    
    def check_frontend_dependencies(self):
        """检查前端依赖"""
        print("🔍 检查前端依赖...")
        
        # 检查前端目录是否存在
        if not self.frontend_dir.exists():
            print(f"❌ React前端目录不存在: {self.frontend_dir}")
            print("💡 请先运行 'npm create vite@latest frontend -- --template react' 创建前端项目")
            return False
        
        # 检查package.json是否存在
        package_json = self.frontend_dir / "package.json"
        if not package_json.exists():
            print(f"❌ 前端项目未初始化: {package_json}")
            print("💡 请先在前端目录运行 'npm install' 安装依赖")
            return False
        
        # 检查node_modules是否存在
        node_modules = self.frontend_dir / "node_modules"
        if not node_modules.exists():
            print("📦 检测到前端依赖未安装，正在安装...")
            try:
                result = subprocess.run(
                    "npm install",
                    cwd=str(self.frontend_dir),
                    capture_output=True,
                    text=True,
                    timeout=120,
                    shell=True
                )
                if result.returncode != 0:
                    print(f"❌ 前端依赖安装失败: {result.stderr}")
                    return False
                print("✅ 前端依赖安装完成")
            except subprocess.TimeoutExpired:
                print("❌ 前端依赖安装超时")
                return False
            except Exception as e:
                print(f"❌ 前端依赖安装失败: {e}")
                return False
        
        print("✅ 前端依赖检查通过")
        return True
    
    def start_frontend_server(self):
        """启动前端开发服务器"""
        print("🌐 启动React前端开发服务器...")
        
        # 检查端口
        if not self.check_port(self.frontend_port, "localhost"):
            if not self.cleanup_port(self.frontend_port, "localhost"):
                print(f"⚠️ 前端端口 {self.frontend_port} 清理失败，尝试使用其他端口")
                self.frontend_port = self.frontend_port + 1
                print(f"🔄 改用前端端口: {self.frontend_port}")
                # 再次检查新端口
                if not self.check_port(self.frontend_port, "localhost"):
                    if not self.cleanup_port(self.frontend_port, "localhost"):
                        print(f"❌ 前端端口 {self.frontend_port} 也无法使用，启动失败")
                        return False
        
        try:
            def run_frontend_server():
                try:
                    # 启动Vite开发服务器
                    env = os.environ.copy()
                    env['VITE_API_URL'] = f"http://localhost:{config.PORT}"
                    
                    # 使用shell=True来确保能找到npm命令
                    cmd = f"npm run dev -- --port {self.frontend_port} --host"
                    process = subprocess.Popen(
                        cmd,
                        cwd=str(self.frontend_dir),
                        env=env,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,
                        text=True,
                        shell=True
                    )
                    
                    self.frontend_process = process
                    print(f"✅ React前端开发服务器启动成功: http://localhost:{self.frontend_port}")
                    
                    # 等待进程结束
                    while self.running and process.poll() is None:
                        time.sleep(1)
                        
                except Exception as e:
                    print(f"⚠️ React前端服务器启动失败: {e}")
            
            frontend_thread = threading.Thread(target=run_frontend_server, daemon=True)
            frontend_thread.start()
            
            # 等待服务器启动
            time.sleep(3)
            return True
            
        except Exception as e:
            self.logger.error(f"React前端服务器启动失败: {e}")
            print(f"❌ React前端服务器启动失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        url = f"http://localhost:{self.frontend_port}"
        
        print(f"🌐 正在打开浏览器: {url}")
        
        try:
            # 检查前端服务是否可用
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(("localhost", self.frontend_port))
            sock.close()
            
            if result == 0:
                webbrowser.open(url)
                print("✅ 浏览器已打开")
            else:
                print(f"⚠️ 前端服务器未启动，请手动访问: {url}")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"💡 请手动访问: {url}")
    
    def display_startup_info(self):
        """显示启动信息"""
        print("\n" + "=" * 60)
        print("🎉 CogBridges 前端服务器启动成功！")
        print("=" * 60)
        print(f"🌐 前端Web地址: http://localhost:{self.frontend_port}")
        print(f"🔗 后端API地址: http://localhost:{config.PORT}")
        print("-" * 60)
        print("💡 使用说明:")
        print("  1. 在Web界面中输入搜索查询")
        print("  2. 系统将自动执行Google搜索 → Reddit数据获取 → 用户分析")
        print("  3. 执行LLM相似subreddit筛选和用户评论动机分析")
        print("  4. 查看完整的分析结果和用户画像")
        print("-" * 60)
        print("🛑 按 Ctrl+C 停止服务")
        print("=" * 60)
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 正在清理前端服务器资源...")
        self.running = False
        
        # 清理React前端进程
        if self.frontend_process:
            try:
                # 终止React开发服务器进程
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ React前端服务器已停止")
            except Exception as e:
                print(f"⚠️ React前端服务器关闭时出错: {e}")
        
        print("👋 前端服务器已停止")
    
    def run(self):
        """运行前端服务器"""
        print("🌟 CogBridges 前端服务器")
        print("=" * 60)
        
        # 检查前端依赖
        if not self.check_frontend_dependencies():
            return False
        
        # 启动前端服务器
        if not self.start_frontend_server():
            return False
        
        # 显示启动信息
        self.display_startup_info()
        
        # 打开浏览器
        self.open_browser()
        
        # 保持运行
        self.running = True
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 用户中断，正在停止前端服务器...")
            self.cleanup()
        
        return True


def main():
    """主函数"""
    server = FrontendServer()
    
    try:
        success = server.run()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止前端服务器...")
        server.cleanup()
    except Exception as e:
        print(f"❌ 前端服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 