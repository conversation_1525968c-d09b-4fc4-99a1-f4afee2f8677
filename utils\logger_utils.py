"""
CogBridges Search - 日志工具
提供统一的日志配置和管理功能
"""

import logging
import sys
from pathlib import Path
from typing import Optional
from datetime import datetime
import json
from config import config


class JSONFormatter(logging.Formatter):
    """JSON格式的日志格式化器"""
    
    def format(self, record):
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_data'):
            log_entry["extra"] = record.extra_data
        
        return json.dumps(log_entry, ensure_ascii=False)


def setup_logger(
    name: str,
    level: Optional[str] = None,
    log_file: Optional[str] = None,
    json_format: bool = False
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        log_file: 日志文件路径
        json_format: 是否使用JSON格式
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 避免重复配置
    if logger.handlers:
        return logger
    
    # 设置日志级别
    log_level = level or config.LOG_LEVEL
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 创建格式化器
    if json_format:
        formatter = JSONFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file or config.SAVE_DETAILED_LOGS:
        if not log_file:
            # 使用默认日志文件
            timestamp = datetime.now().strftime("%Y%m%d")
            log_file = config.LOGS_DIR / f"cogbridges_search_{timestamp}.log"
        
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器实例
    """
    return setup_logger(name)


class LoggerMixin:
    """日志记录器混入类"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志记录器"""
        return get_logger(self.__class__.__name__)


def log_function_call(func):
    """
    函数调用日志装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        
        # 记录函数调用
        logger.debug(f"调用函数 {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    
    return wrapper


async def log_async_function_call(func):
    """
    异步函数调用日志装饰器
    
    Args:
        func: 被装饰的异步函数
        
    Returns:
        装饰后的异步函数
    """
    async def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        
        # 记录函数调用
        logger.debug(f"调用异步函数 {func.__name__}")
        
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"异步函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"异步函数 {func.__name__} 执行失败: {e}")
            raise
    
    return wrapper


def log_api_call(service_name: str, endpoint: str, method: str = "GET"):
    """
    API调用日志装饰器
    
    Args:
        service_name: 服务名称
        endpoint: API端点
        method: HTTP方法
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger(f"api.{service_name}")
            
            # 记录API调用开始
            logger.info(f"开始调用 {service_name} API: {method} {endpoint}")
            start_time = datetime.now()
            
            try:
                result = func(*args, **kwargs)
                
                # 记录API调用成功
                duration = (datetime.now() - start_time).total_seconds()
                logger.info(f"{service_name} API调用成功，耗时: {duration:.2f}秒")
                
                return result
                
            except Exception as e:
                # 记录API调用失败
                duration = (datetime.now() - start_time).total_seconds()
                logger.error(f"{service_name} API调用失败，耗时: {duration:.2f}秒，错误: {e}")
                raise
        
        return wrapper
    return decorator


def create_operation_logger(operation_name: str) -> logging.Logger:
    """
    创建操作专用日志记录器
    
    Args:
        operation_name: 操作名称
        
    Returns:
        操作专用日志记录器
    """
    logger_name = f"operation.{operation_name}"
    
    # 创建专用日志文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = config.LOGS_DIR / f"{operation_name}_{timestamp}.log"
    
    return setup_logger(logger_name, log_file=str(log_file), json_format=True)


def log_search_operation(query: str, results_count: int, duration: float):
    """
    记录搜索操作日志
    
    Args:
        query: 搜索查询
        results_count: 结果数量
        duration: 执行时间
    """
    logger = create_operation_logger("search")
    
    log_data = {
        "operation": "search",
        "query": query,
        "results_count": results_count,
        "duration": duration,
        "timestamp": datetime.now().isoformat()
    }
    
    # 使用extra参数传递结构化数据
    logger.info("搜索操作完成", extra={"extra_data": log_data})


def log_reddit_operation(operation_type: str, data: dict):
    """
    记录Reddit操作日志
    
    Args:
        operation_type: 操作类型 (get_post, get_comments, get_user_history)
        data: 操作数据
    """
    logger = create_operation_logger("reddit")
    
    log_data = {
        "operation": operation_type,
        "data": data,
        "timestamp": datetime.now().isoformat()
    }
    
    logger.info(f"Reddit操作: {operation_type}", extra={"extra_data": log_data})


if __name__ == "__main__":
    # 测试日志配置
    print("CogBridges Search - 日志配置测试")
    print("=" * 50)
    
    # 创建测试日志记录器
    test_logger = setup_logger("test", json_format=True)
    
    # 测试不同级别的日志
    test_logger.debug("这是调试信息")
    test_logger.info("这是信息日志")
    test_logger.warning("这是警告信息")
    test_logger.error("这是错误信息")
    
    # 测试结构化日志
    log_search_operation("测试查询", 5, 1.23)
    log_reddit_operation("get_post", {"post_id": "test123", "subreddit": "test"})
    
    print("✅ 日志测试完成，请检查日志文件")
