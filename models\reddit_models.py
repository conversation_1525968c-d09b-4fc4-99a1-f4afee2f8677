"""
CogBridges Search - 简化版Reddit数据模型
只保留必要的字段，提高性能
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from datetime import datetime


@dataclass
class RedditComment:
    """简化版Reddit评论模型"""
    id: str
    body: str
    author: str
    score: int
    created_utc: float
    parent_id: str
    subreddit: str
    permalink: str
    
    @property
    def created_datetime(self) -> datetime:
        """获取创建时间的datetime对象"""
        return datetime.fromtimestamp(self.created_utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "body": self.body,
            "author": self.author,
            "score": self.score,
            "created_utc": self.created_utc,
            "parent_id": self.parent_id,
            "subreddit": self.subreddit,
            "permalink": self.permalink
        }


@dataclass
class RedditPost:
    """简化版Reddit帖子模型"""
    id: str
    title: str
    selftext: str
    author: str
    score: int
    num_comments: int
    created_utc: float
    subreddit: str
    url: str
    
    @property
    def created_datetime(self) -> datetime:
        """获取创建时间的datetime对象"""
        return datetime.fromtimestamp(self.created_utc)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "title": self.title,
            "selftext": self.selftext,
            "author": self.author,
            "score": self.score,
            "num_comments": self.num_comments,
            "created_utc": self.created_utc,
            "subreddit": self.subreddit,
            "url": self.url
        }


@dataclass
class RedditUser:
    """简化版Reddit用户模型"""
    username: str
    created_utc: float
    comment_karma: int = 0
    link_karma: int = 0
    
    @property
    def created_datetime(self) -> datetime:
        """获取账户创建时间的datetime对象"""
        return datetime.fromtimestamp(self.created_utc)
    
    @property
    def total_karma(self) -> int:
        """获取总karma"""
        return self.comment_karma + self.link_karma
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "username": self.username,
            "created_utc": self.created_utc,
            "comment_karma": self.comment_karma,
            "link_karma": self.link_karma,
            "total_karma": self.total_karma
        }


@dataclass
class UserHistory:
    """简化版用户历史数据模型"""
    user: RedditUser
    posts: List[RedditPost] = None
    comments: List[RedditComment] = None
    
    def __post_init__(self):
        if self.posts is None:
            self.posts = []
        if self.comments is None:
            self.comments = []
    
    @property
    def total_posts(self) -> int:
        """获取总帖子数"""
        return len(self.posts)
    
    @property
    def total_comments(self) -> int:
        """获取总评论数"""
        return len(self.comments)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "user": self.user.to_dict(),
            "posts": [post.to_dict() for post in self.posts],
            "comments": [comment.to_dict() for comment in self.comments],
            "statistics": {
                "total_posts": self.total_posts,
                "total_comments": self.total_comments
            }
        } 