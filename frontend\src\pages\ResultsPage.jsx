import { useState, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import ResultCard from '../components/ResultCard'
import SearchInput from '../components/SearchInput'
import { ArrowLeft, Filter, RefreshCw, AlertCircle } from 'lucide-react'
import { api } from '../services/api'

const ResultsPage = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [results, setResults] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [sortBy, setSortBy] = useState('relevance') // relevance, score, recency
  const [filterBy, setFilterBy] = useState('all') // all, experienced, analytical, supportive

  const query = location.state?.query || ''
  const searchResult = location.state?.searchResult
  const searchTime = location.state?.searchTime || 0

  // 转换后端数据为前端展示格式
  const convertBackendDataToResults = (backendData) => {
    if (!backendData || !backendData.reddit_posts) {
      return []
    }

    const convertedResults = []
    
    // 处理Reddit帖子和评论数据
    backendData.reddit_posts.forEach((postData, postIndex) => {
      const post = postData.post || postData
      const comments = postData.comments || []
      
      // 为每个有效评论创建一个结果项
      comments.forEach((comment, commentIndex) => {
        if (comment.author && comment.body && comment.author !== '[deleted]') {
          const resultId = `${postIndex}-${commentIndex}`
          
          // 获取评论者历史数据
          const commenterHistory = backendData.commenters_history?.[comment.author] || {}
          
          // 生成标签（基于评论内容和用户历史）
          const tags = generateTags(comment, commenterHistory)
          
          // 生成洞察
          const insights = generateInsights(comment, commenterHistory)
          
          convertedResults.push({
            id: resultId,
            author: comment.author,
            subreddit: post.subreddit || 'unknown',
            comment: comment.body.length > 200 ? comment.body.substring(0, 200) + '...' : comment.body,
            summary: comment.body.length > 150 ? comment.body.substring(0, 150) + '...' : comment.body,
            score: comment.score || 0,
            karma: commenterHistory.comment_karma || commenterHistory.karma || 0,
            insights: insights,
            recommendation: generateRecommendation(comment, commenterHistory),
            url: `https://reddit.com${post.permalink || ''}`,
            tags: tags,
            // 保存原始数据用于详情页
            _rawData: {
              post,
              comment,
              commenterHistory,
              backendData
            }
          })
        }
      })
    })

    return convertedResults
  }

  // 生成标签
  const generateTags = (comment, history) => {
    const tags = []
    const text = (comment.body || '').toLowerCase()
    
    // 基于评论内容生成标签
    if (text.includes('经验') || text.includes('我') || text.includes('自己')) {
      tags.push('亲身经历')
    }
    if (text.includes('分析') || text.includes('数据') || text.includes('研究')) {
      tags.push('理性思考')
    }
    if (text.includes('建议') || text.includes('推荐') || text.includes('应该')) {
      tags.push('实用导向')
    }
    if (text.includes('支持') || text.includes('理解') || text.includes('感受')) {
      tags.push('温暖陪伴')
    }
    
    // 基于用户karma等级添加标签
    const karma = history.comment_karma || history.karma || 0
    if (karma > 10000) {
      tags.push('资深用户')
    }
    if (karma > 1000) {
      tags.push('活跃用户')
    }
    
    return tags.length > 0 ? tags : ['深度思考']
  }

  // 生成洞察
  const generateInsights = (comment, history) => {
    const karma = history.comment_karma || history.karma || 0
    const postKarma = history.link_karma || 0
    
    let insights = `该用户在Reddit上有${karma}点评论karma`
    
    if (karma > 5000) {
      insights += '，是一位经验丰富的活跃用户'
    } else if (karma > 1000) {
      insights += '，有一定的社区参与经验'
    }
    
    if (postKarma > 1000) {
      insights += '，经常分享有价值的内容'
    }
    
    return insights
  }

  // 生成推荐理由
  const generateRecommendation = (comment, history) => {
    const karma = history.comment_karma || history.karma || 0
    let reason = '该回答'
    
    if (karma > 5000) {
      reason += '来自高karma用户，'
    }
    
    if (comment.score > 10) {
      reason += '获得了社区的高度认可，'
    }
    
    reason += '内容详实，值得参考'
    
    return reason
  }

  // 加载和处理数据
  useEffect(() => {
    if (!query) {
      navigate('/')
      return
    }

    if (!searchResult) {
      setError('没有搜索结果数据')
      setLoading(false)
      return
    }

    try {
      console.log('处理搜索结果:', searchResult)
      
      if (!searchResult.success) {
        setError(searchResult.error_message || '搜索失败')
        setLoading(false)
        return
      }

      // 转换数据格式
      const convertedResults = convertBackendDataToResults(searchResult)
      console.log('转换后的结果:', convertedResults)
      
      setResults(convertedResults)
      setError(null)
    } catch (err) {
      console.error('数据处理失败:', err)
      setError('数据处理失败: ' + err.message)
    } finally {
      setLoading(false)
    }
  }, [query, searchResult, navigate])

  const handleNewSearch = async (newQuery) => {
    try {
      setLoading(true)
      const searchResult = await api.search(newQuery)
      
      navigate('/loading', { 
        state: { 
          query: newQuery,
          sessionId: searchResult.session_id || Date.now().toString(),
          searchResult,
          timestamp: Date.now()
        } 
      })
    } catch (error) {
      alert(`搜索失败: ${error.message}`)
      setLoading(false)
    }
  }

  const handleViewDetail = (result) => {
    navigate(`/detail/${result.id}`, { 
      state: { 
        result,
        query
      } 
    })
  }

  const handleBookmark = async (result, isBookmarked) => {
    try {
      if (isBookmarked) {
        await api.addBookmark({
          id: result.id,
          query,
          result
        })
        console.log('添加收藏成功')
      } else {
        await api.removeBookmark(result.id)
        console.log('取消收藏成功')
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
    }
  }

  const handleSortChange = (newSort) => {
    setSortBy(newSort)
  }

  const getSortedResults = () => {
    if (!results || results.length === 0) return []
    
    const sorted = [...results].sort((a, b) => {
      switch (sortBy) {
        case 'score':
          return b.score - a.score
        case 'recency':
          return new Date(b.created) - new Date(a.created)
        default: // relevance
          return b.karma - a.karma
      }
    })
    
    if (filterBy === 'all') return sorted
    
    return sorted.filter(result => {
      switch (filterBy) {
        case 'experienced':
          return result.tags.includes('亲身经历')
        case 'analytical':
          return result.tags.includes('理性思考')
        case 'supportive':
          return result.tags.includes('温暖陪伴')
        default:
          return true
      }
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-primary-500 mx-auto mb-4" />
          <p className="text-gray-600">正在处理搜索结果...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  const sortedResults = getSortedResults()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部搜索栏 */}
      <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/')}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            
            <div className="flex-1 max-w-2xl">
              <SearchInput 
                onSearch={handleNewSearch}
                placeholder={query}
                className="w-full"
              />
            </div>

            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span>找到 {sortedResults.length} 个结果</span>
              <span>•</span>
              <span>{(searchTime / 1000).toFixed(1)}秒</span>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-6 py-6">
        <div className="flex gap-6">
          {/* 侧边栏 - 筛选和排序 */}
          <aside className="w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sticky top-24">
              <h3 className="font-semibold text-gray-800 mb-4 flex items-center">
                <Filter className="w-4 h-4 mr-2" />
                筛选选项
              </h3>
              
              {/* 排序选项 */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-2">排序方式</h4>
                <div className="space-y-2">
                  {[
                    { value: 'relevance', label: '相关性' },
                    { value: 'score', label: '点赞数' },
                    { value: 'recency', label: '最新发布' }
                  ].map(option => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        name="sort"
                        value={option.value}
                        checked={sortBy === option.value}
                        onChange={(e) => handleSortChange(e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-600">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* 过滤选项 */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">回答类型</h4>
                <div className="space-y-2">
                  {[
                    { value: 'all', label: '全部' },
                    { value: 'experienced', label: '亲身经历' },
                    { value: 'analytical', label: '理性分析' },
                    { value: 'supportive', label: '情感支持' }
                  ].map(option => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        name="filter"
                        value={option.value}
                        checked={filterBy === option.value}
                        onChange={(e) => setFilterBy(e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-600">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </aside>

          {/* 主要内容区域 */}
          <main className="flex-1">
            {/* 搜索结果标题 */}
            <div className="mb-6">
              <h1 className="text-2xl font-semibold text-gray-800 mb-2">
                关于 "{query}" 的智能分析结果
              </h1>
              <p className="text-gray-600">
                我们为你找到了 {sortedResults.length} 个高质量回答，每个都经过了深度的用户背景分析
              </p>
            </div>

            {/* 结果列表 */}
            {sortedResults.length > 0 ? (
              <div className="space-y-6">
                {sortedResults.map((result, index) => (
                  <div key={result.id} className="animate-fade-in" style={{ animationDelay: `${index * 100}ms` }}>
                    <ResultCard
                      result={result}
                      onViewDetail={handleViewDetail}
                      onBookmark={handleBookmark}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <AlertCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">暂无结果</h3>
                <p className="text-gray-500">没有找到符合条件的回答，请尝试调整筛选条件或重新搜索</p>
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  )
}

export default ResultsPage 