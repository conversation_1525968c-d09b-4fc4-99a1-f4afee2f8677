import { useState, useRef, useEffect } from 'react'
import { Search, X } from 'lucide-react'

const SearchInput = ({ onSearch, placeholder = "我该不该裸辞？", className = "" }) => {
  const [query, setQuery] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const inputRef = useRef(null)

  // 预定义的搜索建议
  const suggestions = [
    "我该不该裸辞？",
    "Python学习路线建议？",
    "如何选择适合的工作？", 
    "副业推荐有哪些？",
    "30岁转行程序员来得及吗？",
    "远程工作的利弊分析",
    "投资理财入门指导",
    "如何提高工作效率？",
    "创业需要准备什么？",
    "学历重要还是能力重要？"
  ]

  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(query.toLowerCase()) && query.length > 0
  ).slice(0, 5)

  const handleSubmit = (e) => {
    e.preventDefault()
    if (query.trim()) {
      onSearch(query.trim())
      setShowSuggestions(false)
    }
  }

  const handleInputChange = (e) => {
    setQuery(e.target.value)
    setSelectedIndex(-1)
    setShowSuggestions(e.target.value.length > 0)
  }

  const handleKeyDown = (e) => {
    if (!showSuggestions || filteredSuggestions.length === 0) return

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < filteredSuggestions.length - 1 ? prev + 1 : prev
        )
        break
      case 'ArrowUp':
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)
        break
      case 'Enter':
        if (selectedIndex >= 0) {
          e.preventDefault()
          setQuery(filteredSuggestions[selectedIndex])
          setShowSuggestions(false)
          setSelectedIndex(-1)
        }
        break
      case 'Escape':
        setShowSuggestions(false)
        setSelectedIndex(-1)
        break
    }
  }

  const selectSuggestion = (suggestion) => {
    setQuery(suggestion)
    setShowSuggestions(false)
    setSelectedIndex(-1)
    inputRef.current?.focus()
  }

  const clearInput = () => {
    setQuery('')
    setShowSuggestions(false)
    inputRef.current?.focus()
  }

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (inputRef.current && !inputRef.current.contains(event.target)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div className={`relative w-full max-w-2xl ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative flex items-center bg-white border border-gray-300 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-200 focus-within:border-primary-500 focus-within:shadow-xl">
          <Search className="w-5 h-5 text-gray-400 ml-6" />
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => query.length > 0 && setShowSuggestions(true)}
            placeholder={placeholder}
            className="flex-1 px-4 py-4 text-lg bg-transparent border-none outline-none"
          />
          {query && (
            <button
              type="button"
              onClick={clearInput}
              className="p-2 mr-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
          <button
            type="submit"
            className="px-6 py-2 mr-2 bg-primary-500 text-white rounded-full hover:bg-primary-600 transition-colors duration-200 disabled:opacity-50"
            disabled={!query.trim()}
          >
            搜索
          </button>
        </div>
      </form>

      {/* 搜索建议下拉框 */}
      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
          {filteredSuggestions.map((suggestion, index) => (
            <div
              key={index}
              onClick={() => selectSuggestion(suggestion)}
              className={`flex items-center px-4 py-3 cursor-pointer transition-colors ${
                index === selectedIndex 
                  ? 'bg-primary-50 text-primary-600' 
                  : 'hover:bg-gray-50'
              }`}
            >
              <Search className="w-4 h-4 text-gray-400 mr-3" />
              <span>{suggestion}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default SearchInput 