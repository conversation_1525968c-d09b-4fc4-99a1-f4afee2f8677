import { useState, useEffect } from 'react'

const StyleDiagnostic = () => {
  const [diagnostics, setDiagnostics] = useState({
    tailwindLoaded: false,
    customColorsWork: false,
    animationsWork: false,
    fontsLoaded: false
  })

  useEffect(() => {
    // 检查Tailwind CSS是否加载
    const testElement = document.createElement('div')
    testElement.className = 'bg-blue-500 text-white p-4 hidden'
    document.body.appendChild(testElement)
    
    const computedStyle = window.getComputedStyle(testElement)
    const bgColor = computedStyle.backgroundColor
    const color = computedStyle.color
    const padding = computedStyle.padding
    
    document.body.removeChild(testElement)
    
    // 检查自定义颜色
    const customColorElement = document.createElement('div')
    customColorElement.className = 'bg-primary-500 hidden'
    document.body.appendChild(customColorElement)
    
    const customStyle = window.getComputedStyle(customColorElement)
    const customBgColor = customStyle.backgroundColor
    
    document.body.removeChild(customColorElement)
    
    // 检查字体
    const fontElement = document.createElement('div')
    fontElement.className = 'font-sans hidden'
    document.body.appendChild(fontElement)
    
    const fontStyle = window.getComputedStyle(fontElement)
    const fontFamily = fontStyle.fontFamily
    
    document.body.removeChild(fontElement)
    
    setDiagnostics({
      tailwindLoaded: bgColor === 'rgb(59, 130, 246)' && color === 'rgb(255, 255, 255)' && padding === '16px',
      customColorsWork: customBgColor === 'rgb(14, 165, 233)',
      animationsWork: true, // 动画需要实际观察
      fontsLoaded: fontFamily.includes('Inter')
    })
  }, [])

  const getStatusIcon = (status) => status ? '✅' : '❌'
  const getStatusText = (status) => status ? '正常' : '异常'
  const getStatusColor = (status) => status ? 'text-green-600' : 'text-red-600'

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <h3 className="font-semibold text-gray-800 mb-3 flex items-center">
        🔧 样式诊断
      </h3>
      
      <div className="space-y-2 text-sm">
        <div className="flex items-center justify-between">
          <span>Tailwind CSS:</span>
          <span className={`flex items-center space-x-1 ${getStatusColor(diagnostics.tailwindLoaded)}`}>
            <span>{getStatusIcon(diagnostics.tailwindLoaded)}</span>
            <span>{getStatusText(diagnostics.tailwindLoaded)}</span>
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span>自定义颜色:</span>
          <span className={`flex items-center space-x-1 ${getStatusColor(diagnostics.customColorsWork)}`}>
            <span>{getStatusIcon(diagnostics.customColorsWork)}</span>
            <span>{getStatusText(diagnostics.customColorsWork)}</span>
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <span>Inter 字体:</span>
          <span className={`flex items-center space-x-1 ${getStatusColor(diagnostics.fontsLoaded)}`}>
            <span>{getStatusIcon(diagnostics.fontsLoaded)}</span>
            <span>{getStatusText(diagnostics.fontsLoaded)}</span>
          </span>
        </div>
      </div>
      
      {/* 实时样式测试 */}
      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="text-xs text-gray-600 mb-2">实时测试:</div>
        <div className="space-y-2">
          <div className="w-full h-2 bg-primary-500 rounded animate-pulse"></div>
          <div className="text-xs font-sans text-primary-600">Primary色彩 + Inter字体</div>
        </div>
      </div>
      
      {/* 调试信息 */}
      <details className="mt-3 text-xs">
        <summary className="cursor-pointer text-gray-500 hover:text-gray-700">
          调试信息
        </summary>
        <div className="mt-2 p-2 bg-gray-50 rounded text-xs font-mono">
          <div>Tailwind: {diagnostics.tailwindLoaded ? 'OK' : 'FAIL'}</div>
          <div>Custom Colors: {diagnostics.customColorsWork ? 'OK' : 'FAIL'}</div>
          <div>Fonts: {diagnostics.fontsLoaded ? 'OK' : 'FAIL'}</div>
        </div>
      </details>
    </div>
  )
}

export default StyleDiagnostic
