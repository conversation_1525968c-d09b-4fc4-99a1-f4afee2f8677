import { Brain, TrendingUp, MessageSquare, Clock } from 'lucide-react'

const InsightPanel = ({ userInsights, className = "" }) => {
  if (!userInsights) return null

  const {
    username,
    karma,
    accountAge,
    topComments,
    personalityAnalysis,
    motivationAnalysis,
    whyRecommended
  } = userInsights

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      {/* 标题栏 */}
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 text-white p-4 rounded-t-lg">
        <div className="flex items-center">
          <Brain className="w-6 h-6 mr-2" />
          <h3 className="text-lg font-semibold">评论者深度分析</h3>
        </div>
        <p className="text-primary-100 text-sm mt-1">基于历史发言的智能洞察</p>
      </div>

      <div className="p-6 space-y-6">
        {/* 用户基本信息 */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div>
            <h4 className="font-semibold text-gray-800">u/{username}</h4>
            <p className="text-gray-600 text-sm">Reddit 用户</p>
          </div>
          <div className="text-right">
            <div className="flex items-center text-sm text-gray-500 mb-1">
              <TrendingUp className="w-4 h-4 mr-1" />
              {karma} karma
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <Clock className="w-4 h-4 mr-1" />
              {accountAge} 账龄
            </div>
          </div>
        </div>

        {/* 历史高赞发言 */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
            <MessageSquare className="w-5 h-5 mr-2 text-primary-500" />
            历史高赞发言片段
          </h4>
          <div className="space-y-3">
            {topComments?.map((comment, index) => (
              <div key={index} className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                <p className="text-gray-700 text-sm leading-relaxed">
                  "{comment.snippet}"
                </p>
                <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                  <span>来自 r/{comment.subreddit}</span>
                  <span>{comment.score} 赞</span>
                </div>
              </div>
            )) || (
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-gray-500 text-sm">暂无历史高赞发言数据</p>
              </div>
            )}
          </div>
        </div>

        {/* 性格分析 */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-3">性格特征分析</h4>
          <div className="p-4 bg-green-50 rounded-lg">
            <p className="text-gray-700 leading-relaxed">
              {personalityAnalysis || "该用户表现出理性思考的特质，善于分析问题的多个层面，发言风格客观平和。"}
            </p>
          </div>
        </div>

        {/* 动机解释 */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-3">动机洞察</h4>
          <div className="p-4 bg-purple-50 rounded-lg">
            <p className="text-gray-700 leading-relaxed">
              {motivationAnalysis || "用户多次表达对自由工作的追求，重视工作与生活的平衡，具有一定的风险承受能力。"}
            </p>
          </div>
        </div>

        {/* 推荐理由 */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-3">为什么我们推荐这个人</h4>
          <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <p className="text-gray-700 leading-relaxed">
              {whyRecommended || "该用户具有丰富的相关经验，发言基于真实经历，能够提供实用的建议和不同的视角，值得参考。"}
            </p>
          </div>
        </div>

        {/* 可信度指标 */}
        <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">85%</div>
            <div className="text-xs text-gray-500">可信度</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">92%</div>
            <div className="text-xs text-gray-500">相关性</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">78%</div>
            <div className="text-xs text-gray-500">有用性</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InsightPanel 