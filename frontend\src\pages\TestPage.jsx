const TestPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-800 mb-8 text-center">
          Tailwind CSS 样式测试
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 卡片1 - 基础样式 */}
          <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">基础样式</h2>
            <p className="text-gray-600 mb-4">这是一个测试卡片，用于验证基础的Tailwind CSS样式。</p>
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
              测试按钮
            </button>
          </div>

          {/* 卡片2 - 颜色系统 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">颜色系统</h2>
            <div className="space-y-2">
              <div className="w-full h-4 bg-red-500 rounded"></div>
              <div className="w-full h-4 bg-green-500 rounded"></div>
              <div className="w-full h-4 bg-blue-500 rounded"></div>
              <div className="w-full h-4 bg-yellow-500 rounded"></div>
              <div className="w-full h-4 bg-purple-500 rounded"></div>
            </div>
          </div>

          {/* 卡片3 - 自定义主题色 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">主题色测试</h2>
            <div className="space-y-3">
              <div className="bg-primary-50 text-primary-700 p-3 rounded">Primary 50</div>
              <div className="bg-primary-500 text-white p-3 rounded">Primary 500</div>
              <div className="bg-primary-600 text-white p-3 rounded">Primary 600</div>
            </div>
          </div>

          {/* 卡片4 - 动画测试 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">动画测试</h2>
            <div className="space-y-3">
              <div className="w-16 h-16 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="w-16 h-16 bg-green-500 rounded-full animate-bounce"></div>
              <div className="w-16 h-16 bg-red-500 rounded-full animate-spin"></div>
            </div>
          </div>

          {/* 卡片5 - 自定义动画 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">自定义动画</h2>
            <div className="space-y-3">
              <div className="w-full h-4 bg-gradient-to-r from-blue-400 to-purple-500 rounded animate-fade-in"></div>
              <div className="w-full h-4 bg-gradient-to-r from-green-400 to-blue-500 rounded animate-slide-up"></div>
              <div className="w-full h-4 bg-gradient-to-r from-pink-400 to-red-500 rounded animate-pulse-slow"></div>
            </div>
          </div>

          {/* 卡片6 - 响应式测试 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">响应式测试</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <div className="bg-blue-100 p-2 rounded text-center text-sm">小屏</div>
              <div className="bg-green-100 p-2 rounded text-center text-sm hidden sm:block">中屏+</div>
            </div>
          </div>
        </div>

        {/* 字体测试 */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">字体测试</h2>
          <div className="space-y-2">
            <p className="font-light text-gray-600">Inter Light - 这是轻字重的文本</p>
            <p className="font-normal text-gray-700">Inter Regular - 这是正常字重的文本</p>
            <p className="font-medium text-gray-800">Inter Medium - 这是中等字重的文本</p>
            <p className="font-semibold text-gray-800">Inter Semibold - 这是半粗字重的文本</p>
            <p className="font-bold text-gray-900">Inter Bold - 这是粗字重的文本</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TestPage
