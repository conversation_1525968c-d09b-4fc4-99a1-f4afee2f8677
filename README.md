# CogBridges - Reddit智能搜索分析平台

<div align="center">

![CogBridges Logo](https://via.placeholder.com/200x80/4A90E2/FFFFFF?text=CogBridges)

**基于Google Custom Search API和Reddit API的Reddit数据智能分析平台**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.0+-green.svg)](https://flask.palletsprojects.com)
[![Google API](https://img.shields.io/badge/Google-Custom%20Search-red.svg)](https://developers.google.com/custom-search)
[![Reddit API](https://img.shields.io/badge/Reddit-API-orange.svg)](https://www.reddit.com/dev/api)

[快速开始](#-快速开始) • [技术架构](#-技术架构) • [前端指南](#-前端使用指南) • [API接口](#-api接口) • [测试验证](#-测试验证)

</div>

---

## 🎯 项目简介

**CogBridges** 是一个基于Google Custom Search API和Reddit API的智能搜索分析平台，实现了从Google搜索到Reddit数据获取，再到LLM驱动的用户画像分析和动机洞察的完整业务流程。

### 🌟 核心特性

- **🔍 Google精准搜索**：集成Google Custom Search API，实现对Reddit内容的精准定位。
- **📊 Reddit深度数据**：并行获取Reddit帖子、评论及用户历史数据。
- **🧠 LLM智能分析**：
  - **相似Subreddit筛选**：利用大语言模型分析用户兴趣，识别相似的subreddits。
  - **用户动机洞察**：深度分析用户评论动机，构建精准的用户画像。
- **⚡ 异步高效处理**：采用`asyncio`进行高效的并发数据获取和LLM调用。
- **🌐 统一Web界面**：提供现代化的前端界面，支持实时搜索和结构化结果展示。
- **🔧 灵活配置**：通过`.env`文件和`config.py`模块，轻松管理API密钥和应用参数。
- **🚀 一键启动**：使用`start_cogbridges.py`脚本，一键启动后端API和前端Web服务。

---

## 🏗 技术架构

### 系统架构概览

CogBridges采用模块化架构设计，实现了清晰的职责分离：

```
CogBridges/
├── api/                    # API模块
│   ├── __init__.py        # API模块初始化
│   └── app.py            # Flask应用和所有API路由
├── frontend/              # React前端应用
│   ├── src/              # 前端源代码
│   ├── package.json      # 前端依赖
│   └── vite.config.js    # Vite配置
├── services/             # 核心服务模块
├── models/               # 数据模型
├── utils/                # 工具类
├── api_server.py         # API服务器启动脚本
├── frontend_server.py    # 前端服务器启动脚本
└── start_cogbridges.py   # 统一启动脚本
```

### 核心业务流程

```mermaid
graph TD
    A[用户在Web界面输入查询] --> B{CogBridges后端API}
    B --> C[步骤1: Google搜索]
    C --> D[步骤2: Reddit数据获取]
    D --> E[步骤3: 用户历史分析]
    E --> F[步骤4: LLM相似性筛选]
    F --> G[步骤5: LLM动机分析]
    G --> H[生成统一分析json数据]
    H --> I[在Web界面展示结果]
```

### 五大核心步骤

| 步骤 | 功能描述 | 技术实现 |
|------|----------|----------|
| **步骤1: Google搜索** | 使用Google Custom Search API搜索Reddit相关内容 | `google-api-python-client` |
| **步骤2: Reddit数据获取** | 并行获取Reddit帖子内容和评论数据 | `asyncpraw` (异步Reddit API库) |
| **步骤3: 用户分析** | 分析评论者历史，构建用户活动概览 | 用户历史数据分析 |
| **步骤4: LLM相似性筛选** | 使用大语言模型筛选与目标subreddit相似的子版块 | Replicate API + `meta/meta-llama-3-8b-instruct` |
| **步骤5: LLM动机分析** | 结合用户历史、相似社区和评论内容，分析用户动机 | Replicate API + 结构化分析Prompt |

### 启动方式

#### 方式一：统一启动（推荐）
```bash
python start_cogbridges.py
```

#### 方式二：分别启动
```bash
# 启动API服务器
python api_server.py

# 启动前端服务器（新终端）
python frontend_server.py
```

#### 方式三：直接启动API
```bash
python -m api.app
```

### 架构优势

1. **职责分离**：API接口、服务器启动、前端启动各司其职
2. **模块化**：每个模块可以独立开发和测试
3. **可维护性**：代码结构清晰，易于维护和扩展
4. **灵活性**：可以选择启动全部服务或单独启动某个服务
5. **可测试性**：API模块可以独立进行单元测试

---

## 🎨 前端使用指南

### 🌟 系统概览

CogBridges v2.0 采用全新的 **React + Tailwind** 前端架构，完全重写了用户界面，实现了**清晰引导 + 渐进加载 + 可解释结果**的三大设计原则。

### 🚀 启动系统

#### 启动命令
```bash
python start_cogbridges.py
```

#### 访问地址
- **前端界面**: http://localhost:5001
- **后端API**: http://localhost:5000
- **健康检查**: http://localhost:5000/api/health

### 🌟 功能特性

#### 📱 **首页搜索界面 (UI0)**
- **极简设计**: GPT 风格的搜索体验
- **智能建议**: 输入时显示相关搜索建议
- **健康检查**: 实时显示后端服务状态
- **示例问题**: 提供热门问题模板
- **键盘导航**: 支持方向键选择建议

#### ⏳ **加载状态页 (UI1)**
- **分阶段进度**: 4个清晰的处理步骤
  1. 🧠 Google搜索中...
  2. 🔍 Reddit数据抓取中...
  3. 👤 分析评论者历史发言...
  4. 🤖 筛选最优回答 + 动机建模...
- **实时进度条**: 显示具体完成百分比
- **人性化文案**: "正在为你找懂你的人"

#### 📊 **搜索结果页 (UI2)**
- **卡片式布局**: 每个结果独立展示
- **智能标签**: 自动生成用户特征标签
- **筛选排序**: 支持相关性/点赞数/时间排序
- **背景洞察**: 显示评论者karma和社区活跃度
- **推荐理由**: 解释为什么推荐这个回答
- **一键操作**: 收藏、查看详情、查看原帖

#### 🔍 **详细分析页 (UI3)**
- **用户深度分析**: 基于历史发言的性格分析
- **动机洞察**: AI分析用户回答的动机和背景
- **可信度评估**: 三维评分（可信度/相关性/实用性）
- **历史发言**: 展示用户过往高赞评论
- **完整内容**: 显示原始回答的完整文本

#### 📚 **历史收藏页 (UI4)**
- **搜索历史**: 记录所有搜索查询
- **收藏管理**: 保存有价值的回答
- **重新搜索**: 一键重跑历史查询
- **数据导出**: 导出个人数据为JSON文件

### 🎯 核心组件

#### `SearchInput`
- 智能搜索框组件
- 自动完成和建议功能
- 支持键盘导航

#### `LoadingSteps`
- 分阶段加载进度显示
- 动画效果和状态管理
- 预计时间显示

#### `ResultCard`
- 搜索结果卡片组件
- 用户信息和标签展示
- 操作按钮集成

#### `InsightPanel`
- 用户深度分析面板
- 历史发言和性格分析
- 可信度指标显示

#### `PersonaTag`
- 动机标签组件
- 8种预定义标签类型
- 多种样式变体

### 🔌 API 集成

#### 主要接口
- `POST /api/search` - 执行搜索
- `GET /api/health` - 健康检查
- `GET /api/history` - 获取搜索历史
- `POST /api/bookmarks` - 添加收藏
- `DELETE /api/bookmarks/:id` - 删除收藏

#### 数据流程
1. **搜索触发**: 用户输入 → API调用 → 返回结果
2. **数据转换**: 后端数据 → 前端格式适配 → UI渲染
3. **状态管理**: Loading状态 → 结果展示 → 详情分析

### 🎨 设计系统

#### 颜色方案
- **主色调**: Primary Blue (#0ea5e9)
- **成功色**: Green (#10b981)
- **警告色**: Yellow (#f59e0b)
- **错误色**: Red (#ef4444)

#### 动画效果
- **淡入动画**: `animate-fade-in`
- **上滑动画**: `animate-slide-up`
- **加载动画**: `animate-spin`

#### 响应式设计
- **移动端优先**: 完全响应式布局
- **断点适配**: sm/md/lg/xl屏幕适配
- **触摸友好**: 大按钮和间距

### 📝 使用流程

#### 1. 搜索查询
1. 访问首页 http://localhost:5001
2. 输入问题（如："我该不该裸辞？"）
3. 选择建议或直接点击搜索

#### 2. 等待分析
1. 观看分阶段加载进度
2. 了解每个处理步骤
3. 等待分析完成

#### 3. 查看结果
1. 浏览搜索结果卡片
2. 查看用户标签和洞察
3. 使用筛选和排序功能

#### 4. 深入分析
1. 点击"查看详情"
2. 阅读完整的用户分析
3. 查看可信度评估

#### 5. 管理收藏
1. 收藏有价值的回答
2. 在历史页面管理收藏
3. 导出个人数据

### 🔧 技术栈

#### 前端
- **React 18** - 现代UI框架
- **Vite** - 快速构建工具
- **Tailwind CSS** - 原子化样式
- **React Router** - 客户端路由
- **Axios** - HTTP请求库
- **Lucide React** - 图标库

#### 后端
- **Flask** - Python Web框架
- **CORS** - 跨域支持
- **Reddit API** - 数据源
- **Google Search API** - 搜索服务
- **LLM Service** - AI分析

### 🚨 故障排除

#### 前端问题
- **白屏**: 检查React控制台错误
- **API失败**: 确认后端服务运行状态
- **样式问题**: 检查Tailwind CSS加载

#### 后端问题
- **API不响应**: 检查Flask服务状态
- **搜索失败**: 验证API密钥配置
- **CORS错误**: 确认跨域设置

#### 常见错误
- **端口冲突**: 修改config.py中的端口设置
- **依赖缺失**: 运行 `pip install -r requirements.txt`
- **配置错误**: 检查config.py中的API密钥

### 🎯 设计原则实现

#### ✅ 清晰引导
- 极简首页设计
- 直观的搜索流程
- 清晰的导航结构

#### ✅ 渐进加载
- 分阶段进度显示
- 平滑的页面转换
- 逐步披露信息

#### ✅ 可解释结果
- 详细的推荐理由
- 用户背景分析
- 可信度评估指标

---

## 🚀 快速开始

### 环境要求

- **Python**: 3.8+
- **系统**: Windows/Linux/macOS
- **网络**: 需要访问Google API、Reddit API和Replicate API

### 安装步骤

```bash
# 1. 克隆项目
git clone <repository-url>
cd CogBridges_v020

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置API密钥（核心步骤）

# 使用.env文件进行配置
# 首先，复制环境变量模板文件
cp .env.example .env

# 然后，编辑 .env 文件，填入你的API密钥
# nano .env 或 vim .env
```

`.env` 文件内容示例：

```plaintext
# Google Custom Search API
GOOGLE_SEARCH_API_KEY="your_google_api_key"
GOOGLE_SEARCH_ENGINE_ID="your_google_search_engine_id"

# Reddit API
REDDIT_CLIENT_ID="your_reddit_client_id"
REDDIT_CLIENT_SECRET="your_reddit_client_secret"
REDDIT_USER_AGENT="CogBridges/2.0 by YourUsername"

# Replicate API (用于LLM功能)
REPLICATE_API_TOKEN="your_replicate_api_token"
```

**安全提醒**：
- `.env` 文件已添加到 `.gitignore`，确保不会被提交到版本控制。
- 妥善保管API密钥，避免泄露。

```bash
# 4. 验证配置（可选）
python config.py

# 5. 启动应用

## 启动方式

### 方式一：统一启动（推荐）
```bash
python start_cogbridges.py
```

### 方式二：分别启动
```bash
# 启动API服务器
python api_server.py

# 启动前端服务器（新终端）
python frontend_server.py
```

### 方式三：直接启动API
```bash
python -m api.app
```

### 验证安装

启动成功后，系统会自动：
- 启动后端API服务器 (默认: `http://127.0.0.1:5000`)
- 启动前端Web服务器 (默认: `http://localhost:5001`)
- 自动在浏览器中打开前端Web界面

你可以访问 `http://127.0.0.1:5000/api/health` 检查API服务状态。

---

## 🔧 API接口

### 核心搜索接口

#### `POST /api/search`

执行完整的搜索和分析流程。

**请求体**:
```json
{
  "query": "搜索查询内容",
  "llm_analysis": true,
  "enhanced_comments": true
}
```

**响应示例**:
```json
{
  "success": true,
  "query": "Should I subscribe to GPT, Claude, or Gemini?",
  "session_id": "20240725_103000_abcdef12",
  "timestamp": "2024-07-25T10:30:45.123Z",
  "total_time": 65.8,
  "google_results": [...],
  "reddit_posts": [...],
  "commenters_history": {...},
  "llm_analysis": {
    "similarity_analysis": {
      "user123": {
        "original_subreddits": ["Python", "Flask", "MachineLearning"],
        "target_analysis": {
          "ClaudeAI": {
            "similar_subreddits": ["MachineLearning"],
            "similarity_count": 1
          }
        },
        "total_similar_count": 1
      }
    },
    "motivation_analysis": {
      "user123": [
        {
          "user_profile": "技术爱好者",
          "comment_motivation": "分享使用经验",
          "key_factors": ["长期使用", "技术对比"]
        }
      ]
    },
    "analysis_summary": {
      "total_users_analyzed": 5,
      "total_motivations_analyzed": 8,
      "top_similar_subreddits": {"MachineLearning": 5, "AI": 3},
      "user_type_distribution": {"developer": 3, "learner": 2},
      "key_insights": [
        "分析了5个用户的subreddit相似性",
        "最常见的相似subreddit是MachineLearning"
      ]
    }
  },
  "statistics": {
    "google_search_time": 2.1,
    "reddit_posts_time": 15.4,
    "commenters_history_time": 25.2,
    "llm_analysis_time": 23.1,
    "google_results_count": 5,
    "reddit_posts_count": 3,
    "commenters_count": 12,
    "similarity_analysis_count": 5,
    "motivation_analysis_count": 8
  }
}
```

### 健康检查与状态接口

#### `GET /api/health`
检查API服务是否正常运行。

**响应**:
```json
{
  "status": "healthy",
  "service": "CogBridges API",
  "timestamp": **********.0
}
```

#### `GET /api/status`
获取服务的详细状态，包括各依赖服务的配置情况。

**响应**:
```json
{
  "service": "CogBridges API",
  "version": "2.0.0",
  "features": {
    "basic_search": true,
    "enhanced_comments": true,
    "llm_analysis": true
  },
  "services": {
    "google_search": true,
    "reddit_api": true,
    "llm_service": true
  },
  "statistics": {...}
}
```

### API接口列表

所有API接口统一在 `api/app.py` 中定义：

- `GET /test` - 简单测试接口
- `GET /api/health` - 健康检查接口
- `POST /api/search` - 执行搜索
- `GET /api/history` - 获取搜索历史
- `GET /api/bookmarks` - 获取收藏列表
- `POST /api/bookmarks` - 添加收藏
- `DELETE /api/bookmarks/<id>` - 删除收藏
- `GET /api/status` - 获取服务状态

---

## 🧠 LLM智能分析功能

### 功能概述

CogBridges集成了基于Replicate API的大语言模型分析功能，提供两个核心能力：

#### 1. 相似Subreddit筛选
- **功能**：从用户的subreddit历史中，筛选出与当前讨论主题相似的子版块。
- **应用场景**：识别用户的核心兴趣圈，为动机分析提供关键上下文。
- **技术实现**：使用`meta/meta-llama-3-8b-instruct`模型进行语义相似性判断。

#### 2. 用户评论动机分析
- **功能**：分析用户在特定帖子中发表评论背后的动机和用户画像。
- **应用场景**：深度理解用户行为，构建更精准的用户画像，洞察用户需求。
- **技术实现**：结合用户历史、相似subreddit数据、评论内容，通过结构化Prompt进行综合分析。

### 分析流程

```mermaid
graph LR
    A[用户历史数据] --> B[提取Subreddit列表]
    B --> C{LLM相似性筛选}
    C --> D[构建相似Subreddit上下文]
    D --> E{LLM动机分析}
    A & C & E --> F[生成用户画像和动机报告]
```

### 配置要求

确保在`.env`文件中正确配置`REPLICATE_API_TOKEN`。

```plaintext
# .env文件
REPLICATE_API_TOKEN="your_replicate_api_token"
```

如果未配置，LLM分析功能将自动跳过。

---

## 🧪 测试验证

### 运行完整集成测试

我们提供了一个全面的端到端集成测试脚本，模拟完整的业务流程。

```bash
# 运行集成测试
python test_integration_complete.py
```

该测试将执行以下步骤：
1. 使用预设查询进行搜索。
2. 验证Google搜索、Reddit数据获取和用户分析功能。
3. 检查LLM相似性筛选和动机分析是否成功执行。
4. 生成详细的测试报告和性能指标，并保存为JSON文件。

### 专项功能测试

除了集成测试，我们还提供针对特定功能的测试脚本：

- `test_comment_motivation.py`: 专注于测试LLM用户评论动机分析功能。
- `test_start_cogbridges_llm.py`: 专门用于测试`start_cogbridges.py`中的LLM分析流程。

运行示例：
```bash
python test_comment_motivation.py
```

---

## ⚙️ 配置说明

所有配置项均通过`.env`文件加载到`config.py`模块中。这种方式实现了配置与代码的分离，提高了安全性。

### 主要配置项 (`config.py`)

- **`GOOGLE_API_KEY`**: Google Custom Search API密钥。
- **`GOOGLE_SEARCH_ENGINE_ID`**: Google Programmable Search Engine ID。
- **`REDDIT_CLIENT_ID`**: Reddit API应用客户端ID。
- **`REDDIT_CLIENT_SECRET`**: Reddit API应用客户端密钥。
- **`REPLICATE_API_TOKEN`**: Replicate API令牌，用于LLM功能。
- **`HOST` / `PORT`**: 后端API服务器的地址和端口。
- **`ENABLE_PROXY` / `HTTP_PROXY`**: 代理设置。

### 获取API密钥

- **Google Custom Search API**:
  1. 访问 [Google Cloud Console](https://console.cloud.google.com/) 创建项目并启用 "Custom Search API"。
  2. 创建API密钥。
  3. 访问 [Programmable Search Engine](https://programmablesearchengine.google.com/) 设置搜索引擎，并关联到你的网站（如`reddit.com`）。

- **Reddit API**:
  1. 访问 [Reddit App Preferences](https://www.reddit.com/prefs/apps)。
  2. 创建一个 "script" 类型的应用以获取`client_id`和`client_secret`。

- **Replicate API**:
  1. 访问 [Replicate](https://replicate.com/) 注册并获取API Token。

---

## 🚀 部署指南

### 本地开发

```bash
# 1. 配置.env文件
# 2. 启动应用
python start_cogbridges.py
```

### 生产部署 (推荐)

#### 使用Gunicorn

```bash
# 安装Gunicorn
pip install gunicorn

# 启动生产服务 (假设你的Flask app实例在start_cogbridges.py中名为app)
# 注意：你需要修改start_cogbridges.py，使其能被Gunicorn调用
# 例如，创建一个app.py来承载Flask应用实例
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

#### Docker部署

项目根目录下提供了`Dockerfile`，可以方便地进行容器化部署。

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

# 复制依赖文件并安装
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制所有项目文件
COPY . .

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "start_cogbridges.py"]
```

构建和运行Docker容器：
```bash
# 构建镜像
docker build -t cogbridges .

# 运行容器
docker run -p 5000:5000 --env-file .env cogbridges
```
**注意**: `--env-file` 参数可以方便地将`.env`文件中的环境变量传递给容器。

---

## 🔧 故障排查

### 1. API密钥配置问题
运行`python config.py`脚本，它会输出当前的配置状态，帮助你检查API密钥是否加载成功。

### 2. 网络连接问题
- **代理**: 如果你所在地区无法直接访问Google或Replicate，请在`.env`文件中配置`ENABLE_PROXY=True`和`HTTP_PROXY`。
- **防火墙**: 确保防火墙没有阻止到`*.googleapis.com`, `www.reddit.com`, `api.replicate.com`的传出连接。

### 3. 依赖安装问题
如果遇到依赖问题，可以尝试创建一个新的虚拟环境并重新安装：
```bash
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
```

---

## 📈 项目特点

### 技术亮点

- **真实API集成**: 完全基于官方API（Google, Reddit, Replicate），非爬虫，稳定可靠。
- **异步优先**: 大量使用`asyncio`和`asyncpraw`，最大化I/O效率。
- **LLM深度应用**: 不仅仅是调用LLM，而是设计了多步骤的分析链，将LLM分析能力与传统数据挖掘结合。
- **模块化设计**: 服务、模型和工具类分离，易于扩展和维护。
- **配置与代码分离**: 使用`.env`管理敏感信息，安全且灵活。

### 应用场景

- **市场调研**: 深度分析Reddit用户对特定产品或服务的讨论热点和情感倾向。
- **用户研究**: 快速构建目标用户群体的兴趣画像和行为动机。
- **内容策略**: 挖掘热门话题和用户互动模式，指导内容创作。
- **竞品分析**: 洞察用户对竞争对手产品的看法和核心需求。

---

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 `LICENSE` 文件。

---

## 📞 联系我们

- **项目主页**: [https://github.com/your-org/cogbridges](https://github.com/your-org/cogbridges)
- **问题反馈**: [https://github.com/your-org/cogbridges/issues](https://github.com/your-org/cogbridges/issues)

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

Made with ❤️ by the CogBridges Team

**CogBridges** - 洞察Reddit，连接智慧 🔍✨

</div>
