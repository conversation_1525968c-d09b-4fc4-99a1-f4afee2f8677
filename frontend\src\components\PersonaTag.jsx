import { 
  Heart, 
  Brain, 
  Target, 
  Users, 
  Lightbulb, 
  Shield,
  Star,
  Zap
} from 'lucide-react'

const PersonaTag = ({ tag, size = 'md', variant = 'default' }) => {
  const getTagConfig = (tagName) => {
    const configs = {
      '亲身经历': { icon: Heart, color: 'rose', label: '亲身经历' },
      '理性思考': { icon: Brain, color: 'blue', label: '理性思考' },
      '专业见解': { icon: Target, color: 'green', label: '专业见解' },
      '温暖陪伴': { icon: Users, color: 'pink', label: '温暖陪伴' },
      '创新想法': { icon: Lightbulb, color: 'yellow', label: '创新想法' },
      '可靠建议': { icon: Shield, color: 'indigo', label: '可靠建议' },
      '深度思考': { icon: Star, color: 'purple', label: '深度思考' },
      '实用导向': { icon: Zap, color: 'orange', label: '实用导向' }
    }
    return configs[tagName] || { icon: Star, color: 'gray', label: tagName }
  }

  const sizeConfig = {
    sm: {
      container: 'px-2 py-1 text-xs',
      icon: 'w-3 h-3',
      text: 'text-xs'
    },
    md: {
      container: 'px-3 py-1.5 text-sm',
      icon: 'w-4 h-4',
      text: 'text-sm'
    },
    lg: {
      container: 'px-4 py-2 text-base',
      icon: 'w-5 h-5',
      text: 'text-base'
    }
  }

  const variantConfig = {
    default: 'border border-opacity-30',
    filled: '',
    outline: 'border-2 bg-transparent'
  }

  const { icon: Icon, color, label } = getTagConfig(tag)
  const sizeClasses = sizeConfig[size]
  const variantClasses = variantConfig[variant]

  const colorClasses = {
    rose: variant === 'filled' 
      ? 'bg-rose-100 text-rose-700 border-rose-200'
      : variant === 'outline'
      ? 'border-rose-500 text-rose-600 hover:bg-rose-50'
      : 'bg-rose-50 text-rose-600 border-rose-200',
    blue: variant === 'filled'
      ? 'bg-blue-100 text-blue-700 border-blue-200'
      : variant === 'outline'
      ? 'border-blue-500 text-blue-600 hover:bg-blue-50'
      : 'bg-blue-50 text-blue-600 border-blue-200',
    green: variant === 'filled'
      ? 'bg-green-100 text-green-700 border-green-200'
      : variant === 'outline'
      ? 'border-green-500 text-green-600 hover:bg-green-50'
      : 'bg-green-50 text-green-600 border-green-200',
    pink: variant === 'filled'
      ? 'bg-pink-100 text-pink-700 border-pink-200'
      : variant === 'outline'
      ? 'border-pink-500 text-pink-600 hover:bg-pink-50'
      : 'bg-pink-50 text-pink-600 border-pink-200',
    yellow: variant === 'filled'
      ? 'bg-yellow-100 text-yellow-700 border-yellow-200'
      : variant === 'outline'
      ? 'border-yellow-500 text-yellow-600 hover:bg-yellow-50'
      : 'bg-yellow-50 text-yellow-600 border-yellow-200',
    indigo: variant === 'filled'
      ? 'bg-indigo-100 text-indigo-700 border-indigo-200'
      : variant === 'outline'
      ? 'border-indigo-500 text-indigo-600 hover:bg-indigo-50'
      : 'bg-indigo-50 text-indigo-600 border-indigo-200',
    purple: variant === 'filled'
      ? 'bg-purple-100 text-purple-700 border-purple-200'
      : variant === 'outline'
      ? 'border-purple-500 text-purple-600 hover:bg-purple-50'
      : 'bg-purple-50 text-purple-600 border-purple-200',
    orange: variant === 'filled'
      ? 'bg-orange-100 text-orange-700 border-orange-200'
      : variant === 'outline'
      ? 'border-orange-500 text-orange-600 hover:bg-orange-50'
      : 'bg-orange-50 text-orange-600 border-orange-200',
    gray: variant === 'filled'
      ? 'bg-gray-100 text-gray-700 border-gray-200'
      : variant === 'outline'
      ? 'border-gray-500 text-gray-600 hover:bg-gray-50'
      : 'bg-gray-50 text-gray-600 border-gray-200'
  }

  return (
    <span className={`
      inline-flex items-center gap-1.5 rounded-full font-medium
      transition-colors duration-200 cursor-default
      ${sizeClasses.container}
      ${variantClasses}
      ${colorClasses[color]}
    `}>
      <Icon className={sizeClasses.icon} />
      <span className={sizeClasses.text}>#{label}</span>
    </span>
  )
}

export default PersonaTag 