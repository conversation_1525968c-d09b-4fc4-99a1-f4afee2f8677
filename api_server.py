#!/usr/bin/env python3
"""
CogBridges API 服务器启动脚本
独立的API服务器启动器，不包含前端启动逻辑
"""

import sys
import os
import time
import socket
import psutil
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger_utils import get_logger
from api.app import create_app


class APIServer:
    """API服务器启动器"""
    
    def __init__(self):
        """初始化API服务器"""
        self.logger = get_logger(__name__)
        self.app = None
    
    def check_port(self, port, host="localhost"):
        """检查端口占用"""
        print(f"🔍 检查端口 {host}:{port} 占用情况...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"⚠️ 端口 {port} 已被占用")
                return False
            else:
                print(f"✅ 端口 {port} 可用")
                return True
                
        except Exception as e:
            self.logger.error(f"检查端口 {port} 时出错: {e}")
            print(f"⚠️ 检查端口 {port} 时出错: {e}")
            return False
    
    def cleanup_port(self, port, host="localhost"):
        """清理端口占用"""
        print(f"🧹 正在清理端口 {host}:{port}...")
        
        try:
            # 查找占用端口的进程
            processes_using_port = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    connections = proc.connections()
                    for conn in connections:
                        if conn.laddr.port == port:
                            processes_using_port.append({
                                'pid': proc.pid,
                                'name': proc.name(),
                                'cmdline': ' '.join(proc.cmdline()) if proc.cmdline() else ''
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            if processes_using_port:
                print(f"📋 发现 {len(processes_using_port)} 个进程占用端口 {port}:")
                for proc_info in processes_using_port:
                    print(f"   - PID: {proc_info['pid']}, 名称: {proc_info['name']}")
                
                # 询问是否清理端口
                try:
                    response = input(f"\n❓ 是否终止占用端口 {port} 的进程？(y/N): ").strip().lower()
                    if response in ['y', 'yes']:
                        killed_count = 0
                        for proc_info in processes_using_port:
                            try:
                                proc = psutil.Process(proc_info['pid'])
                                proc.terminate()
                                proc.wait(timeout=5)
                                print(f"✅ 已终止进程 PID {proc_info['pid']} ({proc_info['name']})")
                                killed_count += 1
                            except (psutil.NoSuchProcess, psutil.TimeoutExpired, psutil.AccessDenied) as e:
                                print(f"⚠️ 无法终止进程 PID {proc_info['pid']}: {e}")
                        
                        if killed_count > 0:
                            print(f"✅ 已清理 {killed_count} 个占用端口的进程")
                            time.sleep(2)
                            return True
                        else:
                            print("❌ 未能清理任何进程")
                            return False
                    else:
                        print("⏭️ 跳过端口清理")
                        return False
                except KeyboardInterrupt:
                    print("\n⏭️ 用户取消，跳过端口清理")
                    return False
            else:
                print(f"⚠️ 端口 {port} 被占用但无法识别占用进程")
                return False
                
        except Exception as e:
            self.logger.error(f"清理端口 {port} 时出错: {e}")
            print(f"⚠️ 清理端口 {port} 时出错: {e}")
            return False
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查依赖...")
        
        required_packages = ['flask', 'flask_cors', 'requests', 'praw', 'asyncpraw', 'tenacity', 'psutil']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ 缺少以下依赖包:")
            for package in missing_packages:
                print(f"   - {package}")
            print(f"\n💡 请运行以下命令安装依赖:")
            print(f"   pip install -r requirements.txt")
            return False
        
        print("✅ 所有依赖已安装")
        return True
    
    def check_configuration(self):
        """检查配置"""
        print("🔧 检查配置...")
        
        # 检查必要的配置项
        required_configs = [
            ('GOOGLE_API_KEY', config.GOOGLE_API_KEY),
            ('GOOGLE_SEARCH_ENGINE_ID', config.GOOGLE_SEARCH_ENGINE_ID),
            ('REDDIT_CLIENT_ID', config.REDDIT_CLIENT_ID),
            ('REDDIT_CLIENT_SECRET', config.REDDIT_CLIENT_SECRET)
        ]
        
        missing_configs = []
        
        for name, value in required_configs:
            if not value or value == "your_api_key_here":
                missing_configs.append(name)
        
        if missing_configs:
            print(f"❌ 缺少以下配置:")
            for config_name in missing_configs:
                print(f"   - {config_name}")
            print(f"\n💡 请在config.py中配置这些参数")
            return False
        
        # 检查LLM服务配置（可选）
        if not config.replicate_configured:
            print("⚠️ Replicate API未配置，LLM分析功能将不可用")
        else:
            print("✅ LLM服务配置检查通过")
        
        print("✅ 配置检查通过")
        return True
    
    def start_server(self):
        """启动API服务器"""
        print("🚀 启动CogBridges API服务器...")
        
        # 检查端口
        if not self.check_port(config.PORT, config.HOST):
            if not self.cleanup_port(config.PORT, config.HOST):
                print(f"❌ 端口 {config.PORT} 无法使用，启动失败")
                return False
        
        try:
            # 创建Flask应用
            self.app = create_app()
            
            print(f"📍 正在启动API服务器: {config.HOST}:{config.PORT}")
            print("=" * 60)
            print("🎉 CogBridges API 服务器启动成功！")
            print("=" * 60)
            print(f"🔗 API地址: http://{config.HOST}:{config.PORT}")
            print(f"📚 健康检查: http://{config.HOST}:{config.PORT}/api/health")
            print(f"🔍 测试接口: http://{config.HOST}:{config.PORT}/test")
            print(f"📊 状态查询: http://{config.HOST}:{config.PORT}/api/status")
            print("-" * 60)
            print("💡 可用的API接口:")
            print("  POST /api/search     - 执行搜索")
            print("  GET  /api/history    - 获取搜索历史")
            print("  GET  /api/bookmarks  - 获取收藏列表")
            print("  POST /api/bookmarks  - 添加收藏")
            print("  DELETE /api/bookmarks/<id> - 删除收藏")
            print("-" * 60)
            print("🛑 按 Ctrl+C 停止服务")
            print("=" * 60)
            
            # 启动Flask应用
            self.app.run(
                host=config.HOST,
                port=config.PORT,
                debug=False,
                threaded=True,
                use_reloader=False
            )
            
        except Exception as e:
            self.logger.error(f"API服务器启动失败: {e}")
            print(f"❌ API服务器启动失败: {e}")
            return False
    
    def run(self):
        """运行API服务器"""
        print("🌟 CogBridges API 服务器")
        print("=" * 60)
        
        # 检查依赖和配置
        if not self.check_dependencies():
            return False
        
        if not self.check_configuration():
            return False
        
        # 启动服务器
        return self.start_server()


def main():
    """主函数"""
    server = APIServer()
    
    try:
        success = server.run()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止API服务器...")
    except Exception as e:
        print(f"❌ API服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 