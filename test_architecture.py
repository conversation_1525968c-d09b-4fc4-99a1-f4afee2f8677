#!/usr/bin/env python3
"""
测试重构后的架构
验证API模块和启动脚本是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_api_module():
    """测试API模块是否可以正常导入"""
    print("🔍 测试API模块导入...")
    
    try:
        from api.app import create_app
        print("✅ API模块导入成功")
        
        # 测试创建Flask应用
        app = create_app()
        print("✅ Flask应用创建成功")
        
        # 检查应用是否有预期的路由
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(rule.rule)
        
        expected_routes = [
            '/test',
            '/api/health',
            '/api/search',
            '/api/history',
            '/api/bookmarks',
            '/api/status'
        ]
        
        print(f"📋 发现的路由: {routes}")
        
        missing_routes = []
        for route in expected_routes:
            if route not in routes:
                missing_routes.append(route)
        
        if missing_routes:
            print(f"❌ 缺少路由: {missing_routes}")
            return False
        else:
            print("✅ 所有预期路由都存在")
            return True
            
    except ImportError as e:
        print(f"❌ API模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 创建Flask应用失败: {e}")
        return False


def test_config_import():
    """测试配置模块是否可以正常导入"""
    print("\n🔍 测试配置模块导入...")
    
    try:
        from config import config
        print("✅ 配置模块导入成功")
        
        # 检查必要的配置项
        required_configs = [
            'HOST',
            'PORT',
            'GOOGLE_API_KEY',
            'GOOGLE_SEARCH_ENGINE_ID',
            'REDDIT_CLIENT_ID',
            'REDDIT_CLIENT_SECRET'
        ]
        
        missing_configs = []
        for config_name in required_configs:
            if not hasattr(config, config_name):
                missing_configs.append(config_name)
        
        if missing_configs:
            print(f"⚠️ 缺少配置项: {missing_configs}")
        else:
            print("✅ 所有必要配置项都存在")
        
        return True
        
    except ImportError as e:
        print(f"❌ 配置模块导入失败: {e}")
        return False


def test_services_import():
    """测试服务模块是否可以正常导入"""
    print("\n🔍 测试服务模块导入...")
    
    try:
        from services.cogbridges_service import CogBridgesService
        print("✅ CogBridges服务模块导入成功")
        return True
        
    except ImportError as e:
        print(f"⚠️ 服务模块导入失败（缺少可选依赖）: {e}")
        print("📝 这是正常的，LLM分析功能将不可用")
        return True  # 改为True，因为这是可选依赖


def test_utils_import():
    """测试工具模块是否可以正常导入"""
    print("\n🔍 测试工具模块导入...")
    
    try:
        from utils.logger_utils import get_logger
        print("✅ 日志工具模块导入成功")
        
        # 测试创建logger
        logger = get_logger(__name__)
        print("✅ Logger创建成功")
        return True
        
    except ImportError as e:
        print(f"❌ 工具模块导入失败: {e}")
        return False


def test_file_structure():
    """测试文件结构是否正确"""
    print("\n🔍 测试文件结构...")
    
    required_files = [
        'api/__init__.py',
        'api/app.py',
        'api_server.py',
        'frontend_server.py',
        'start_cogbridges.py',
        'config.py',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        print("✅ 所有必要文件都存在")
        return True


def main():
    """主测试函数"""
    print("🌟 CogBridges 架构测试")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("配置模块", test_config_import),
        ("工具模块", test_utils_import),
        ("服务模块", test_services_import),
        ("API模块", test_api_module),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！架构重构成功！")
        print("\n💡 现在你可以使用以下方式启动服务：")
        print("  1. 统一启动: python start_cogbridges.py")
        print("  2. 分别启动: python api_server.py 和 python frontend_server.py")
        print("  3. 直接启动API: python -m api.app")
        return True
    else:
        print("❌ 部分测试失败，请检查架构重构")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 