import { useState, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import LoadingSteps from '../components/LoadingSteps'
import { api } from '../services/api'

const LoadingPage = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0)
  const [searchStatus, setSearchStatus] = useState('starting') // starting, searching, completed, error
  const [errorMessage, setErrorMessage] = useState('')

  const query = location.state?.query || '搜索查询'
  const sessionId = location.state?.sessionId

  // 处理搜索结果
  useEffect(() => {
    if (!location.state?.query) {
      // 如果没有查询参数，重定向到首页
      navigate('/')
      return
    }

    // 开始实际的搜索过程
    startSearch()
  }, [query, navigate]) // 只依赖query和navigate，避免重复执行

  const startSearch = async () => {
    try {
      setSearchStatus('searching')
      
      // 模拟搜索步骤进度
      const steps = [
        { name: '翻译查询', duration: 1000 },
        { name: '搜索Google', duration: 2000 },
        { name: '获取Reddit帖子', duration: 3000 },
        { name: '分析用户历史', duration: 4000 },
        { name: 'LLM深度分析', duration: 5000 }
      ]

      let totalDuration = 0
      steps.forEach((step, index) => {
        setTimeout(() => {
          setCurrentStep(index)
          setProgress(((index + 1) / steps.length) * 80) // 前80%用于显示步骤进度
        }, totalDuration)
        totalDuration += step.duration
      })

      // 在后台执行实际的搜索
      const searchResult = await api.search(query)
      
      // 搜索完成，更新进度到100%
      setProgress(100)
      setSearchStatus('completed')

      // 等待一小段时间让用户看到完成状态
      setTimeout(() => {
        navigate('/results', { 
          state: { 
            query,
            searchResult,
            searchTime: totalDuration / 1000 // 转换为秒
          } 
        })
      }, 1000)

    } catch (error) {
      console.error('搜索失败:', error)
      setSearchStatus('error')
      setErrorMessage(error.message)
      
      // 3秒后返回首页
      setTimeout(() => {
        alert(`搜索失败: ${error.message}`)
        navigate('/')
      }, 3000)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">C</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-800">CogBridges</h1>
              <p className="text-sm text-gray-500">正在分析搜索结果...</p>
            </div>
          </div>
          
          <div className="text-right">
            <p className="text-sm text-gray-600">搜索内容</p>
            <p className="font-medium text-gray-800 max-w-md truncate">"{query}"</p>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="flex-1 flex items-center justify-center px-6 py-12">
        <div className="w-full max-w-3xl">
          {/* 总体进度条 */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700">分析进度</span>
              <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>

          {/* 加载步骤 */}
          <LoadingSteps 
            currentStep={currentStep}
            onComplete={() => {
              // 这个回调不再需要，因为useEffect已经处理了跳转
            }}
          />

          {/* 底部提示信息 */}
          <div className="mt-12 text-center">
            {searchStatus === 'error' ? (
              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-red-800 mb-2">
                  搜索失败
                </h3>
                <p className="text-red-700 leading-relaxed">
                  {errorMessage || '搜索过程中发生错误，请稍后重试'}
                </p>
              </div>
            ) : searchStatus === 'completed' ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-green-800 mb-2">
                  搜索完成！
                </h3>
                <p className="text-green-700 leading-relaxed">
                  正在跳转到结果页面...
                </p>
              </div>
            ) : (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-medium text-blue-800 mb-2">
                  正在深度分析数据
                </h3>
                <p className="text-blue-700 leading-relaxed">
                  我们已经完成了数据收集，正在进行深度分析，为你筛选出最有价值、最贴近你需求的回答。
                  每一个推荐都基于用户的真实经历和深度思考。
                </p>
              </div>
            )}
          </div>

          {/* 取消按钮 */}
          <div className="mt-8 text-center">
            <button
              onClick={() => navigate('/')}
              className="px-6 py-2 text-gray-500 hover:text-gray-700 transition-colors"
            >
              返回首页
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}

export default LoadingPage 