import axios from 'axios'

// 从环境变量获取API基础URL，默认使用localhost:5000
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000'

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 120000, // 120秒超时（2分钟），因为LLM分析可能需要较长时间
  headers: {
    'Content-Type': 'application/json',
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('❌ API请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log(`✅ API响应: ${response.config.method?.toUpperCase()} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('❌ API响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// API方法
export const api = {
  // 健康检查
  async healthCheck() {
    try {
      const response = await apiClient.get('/api/health')
      return response.data
    } catch (error) {
      throw new Error(`健康检查失败: ${error.message}`)
    }
  },

  // 搜索接口
  async search(query) {
    try {
      const response = await apiClient.post('/api/search', { 
        query,
        enhanced: true,  // 启用增强功能
        llm_analysis: true  // 启用LLM分析
      })
      return response.data
    } catch (error) {
      throw new Error(`搜索失败: ${error.message}`)
    }
  },

  // 获取搜索进度（如果后端支持）
  async getSearchProgress(sessionId) {
    try {
      const response = await apiClient.get(`/api/progress/${sessionId}`)
      return response.data
    } catch (error) {
      // 如果没有进度接口，返回默认状态
      return { status: 'completed', progress: 100 }
    }
  },

  // 获取搜索结果详情
  async getResultDetail(resultId) {
    try {
      const response = await apiClient.get(`/api/result/${resultId}`)
      return response.data
    } catch (error) {
      throw new Error(`获取详情失败: ${error.message}`)
    }
  },

  // 获取搜索历史
  async getSearchHistory() {
    try {
      const response = await apiClient.get('/api/history')
      return response.data
    } catch (error) {
      // 如果没有历史接口，返回模拟数据
      console.warn('历史记录API不可用，使用模拟数据')
      return [
        {
          id: '1',
          query: '如何度过创业低谷期',
          timestamp: Date.now() - 3600000, // 1小时前
          resultsCount: 5,
          searchTime: 43.01,
          success: true
        },
        {
          id: '2',
          query: '30岁转行程序员来得及吗',
          timestamp: Date.now() - 86400000, // 1天前
          resultsCount: 3,
          searchTime: 35.2,
          success: true
        }
      ]
    }
  },

  // 添加收藏
  async addBookmark(resultData) {
    try {
      const response = await apiClient.post('/api/bookmarks', resultData)
      return response.data
    } catch (error) {
      throw new Error(`添加收藏失败: ${error.message}`)
    }
  },

  // 获取收藏列表
  async getBookmarks() {
    try {
      const response = await apiClient.get('/api/bookmarks')
      return response.data
    } catch (error) {
      // 如果没有收藏接口，返回模拟数据
      console.warn('收藏列表API不可用，使用模拟数据')
      return [
        {
          id: 'bookmark_1',
          query: '如何度过创业低谷期',
          title: '创业低谷期的心理调适',
          content: '创业路上遇到低谷是正常的，关键是如何调整心态...',
          source: 'r/entrepreneur',
          timestamp: Date.now() - 7200000, // 2小时前
          tags: ['创业', '心理健康', '经验分享']
        }
      ]
    }
  },

  // 删除收藏
  async removeBookmark(bookmarkId) {
    try {
      const response = await apiClient.delete(`/api/bookmarks/${bookmarkId}`)
      return response.data
    } catch (error) {
      throw new Error(`删除收藏失败: ${error.message}`)
    }
  }
}

// 导出默认实例
export default api 